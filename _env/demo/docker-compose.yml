version: "3"
services:
  magento:
    container_name: vip_watches_magento
    image: todor0pfg/mage1:store-demo
    volumes:
      - ../../magento:/magento
    networks:
      - vip_watches_internal
    depends_on:
      - cache
      - db
    deploy:
      resources:
        limits:
          memory: 2500MB

  server:
    container_name: vip_watches_server
    image: todor0pfg/env:nginx1.23-amd64
    volumes:
      - ../../magento:/magento
      - ./nginx/mage.conf:/etc/nginx/conf.d/mage.conf:ro
      - ../_logs/server:/var/log/nginx/
    ports:
      - "8580:80"
    networks:
      - vip_watches_internal
    depends_on:
      - magento
    deploy:
      resources:
        limits:
          memory: 500MB

  cache:
    container_name: vip_watches_cache
    command: redis-server /usr/local/etc/redis/redis.conf
    image: redis:7-alpine
    volumes:
      - ./_env/demo/cache/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - vip_watches_internal
    deploy:
      resources:
        limits:
          memory: 256MB

  db:
    container_name: vip_watches_db
    image: mysql:8
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=vip_watches
    volumes:
      - ./_env/demo/mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
      - ./.database/:/var/lib/mysql
      - ./_env/_logs/db/:/var/log/mysql
      - ./import-folder/:/tmp/import
    ports:
      - "33850:3306"

#  cron: is disabled on Demo

networks:
  vip_watches_internal:
    driver: bridge
