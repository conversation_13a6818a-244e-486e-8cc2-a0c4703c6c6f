/*
 * Ext JS Library 1.1 Beta 1
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

.ext-el-mask {
    z-index: 20000;
    position: absolute;
    top: 0;
    left: 0;
    -moz-opacity: 0.5;
    opacity: .50;
    filter: alpha(opacity=50);
    background-color: #CCC;
    width: 100%;
    height: 100%;
    zoom: 1;
}
.ext-el-mask-msg {
    z-index: 20001;
    position: absolute;
    top: 0;
    left: 0;
    border:1px solid #6593cf;
    background: #c3daf9 url(../images/default/box/tb-blue.gif) repeat-x 0 -16px;
    padding:2px;
}
.ext-el-mask-msg div {
    padding:5px 10px 5px 10px;
    background: #eee;
    border:1px solid #a3bad9;
    color:#333;
    font:normal 12px tahoma, arial, helvetica, sans-serif;
    cursor:wait;
}

.ext-shim {
    position:absolute;
    visibility:hidden;
    left:0;
    top:0;
    overflow:hidden;
}
.ext-ie .ext-shim {
    filter: alpha(opacity=0);
}

.x-mask-loading div {
    padding:5px 10px 5px 25px;
    background: #eee url( '../images/default/grid/loading.gif' ) no-repeat 5px 5px;
    line-height: 16px;
}

/* class for hiding elements without using display:none */
.x-hidden {
    position:absolute;
    left:-2000px;
    top:-2000px;
    visibility:hidden;
}

.x-masked {
    overflow: hidden !important;
}

.x-masked select, .x-masked object, .x-masked embed {
    visibility: hidden;
}

.x-layer {
    visibility: hidden;
}

.x-unselectable, .x-unselectable * {
    -moz-user-select: none;
    -khtml-user-select: none;
}

.x-repaint {
    zoom: 1;
    background-color: transparent;
    -moz-outline: none;
}

.x-item-disabled {
    color: gray;
    cursor: default;
    opacity: .6;
    -moz-opacity: .6;
    filter: alpha(opacity=60);
}

.x-item-disabled * {
    color: gray;
    cursor: default !important;
}

.x-splitbar-proxy {
    position: absolute;
    visibility: hidden;
    z-index: 20001;
    background: #aaa;
    zoom: 1;
    line-height: 1px;
    font-size: 1px;
    overflow: hidden;
}

.x-splitbar-h, .x-splitbar-proxy-h {
    cursor: e-resize;
    cursor: col-resize;
}

.x-splitbar-v, .x-splitbar-proxy-v {
    cursor: s-resize;
    cursor: row-resize;
}

.x-color-palette {
    width: 150px;
    height: 92px;
    cursor: pointer;
}

.x-color-palette a {
    border: 1px solid #fff;
    float: left;
    padding: 2px;
    text-decoration: none;
    -moz-outline: 0 none;
    outline: 0 none;
    cursor: pointer;
}

.x-color-palette a:hover, .x-color-palette a.x-color-palette-sel {
    border: 1px solid #8BB8F3;
    background: #deecfd;
}

.x-color-palette em {
    display: block;
    border: 1px solid #ACA899;
}

.x-color-palette em span {
    cursor: pointer;
    display: block;
    height: 10px;
    line-height: 10px;
    width: 10px;
}

.x-ie-shadow {
    display: none;
    position: absolute;
    overflow: hidden;
    left:0;
    top:0;
    background:#777;
    zoom:1;
}

.x-shadow {
    display: none;
    position: absolute;
    overflow: hidden;
    left:0;
    top:0;
}

.x-shadow * {
    overflow: hidden;
}

.x-shadow * {
    padding: 0;
    border: 0;
    margin: 0;
    clear: none;
    zoom: 1;
}

/* top  bottom */
.x-shadow .xstc, .x-shadow .xsbc {
    height: 6px;
    float: left;
}

/* corners */
.x-shadow .xstl, .x-shadow .xstr, .x-shadow .xsbl, .x-shadow .xsbr {
    width: 6px;
    height: 6px;
    float: left;
}

/* sides */
.x-shadow .xsc {
    width: 100%;
}

.x-shadow .xsml, .x-shadow .xsmr {
    width: 6px;
    float: left;
    height: 100%;
}

.x-shadow .xsmc {
    float: left;
    height: 100%;
    background: transparent url( ../images/default/shadow-c.png );
}

.x-shadow .xst, .x-shadow .xsb {
    height: 6px;
    overflow: hidden;
    width: 100%;
}

.x-shadow .xsml {
    background: transparent url( ../images/default/shadow-lr.png ) repeat-y 0 0;
}

.x-shadow .xsmr {
    background: transparent url( ../images/default/shadow-lr.png ) repeat-y -6px 0;
}

.x-shadow .xstl {
    background: transparent url( ../images/default/shadow.png ) no-repeat 0 0;
}

.x-shadow .xstc {
    background: transparent url( ../images/default/shadow.png ) repeat-x 0 -30px;
}

.x-shadow .xstr {
    background: transparent url( ../images/default/shadow.png ) repeat-x 0 -18px;
}

.x-shadow .xsbl {
    background: transparent url( ../images/default/shadow.png ) no-repeat 0 -12px;
}

.x-shadow .xsbc {
    background: transparent url( ../images/default/shadow.png ) repeat-x 0 -36px;
}

.x-shadow .xsbr {
    background: transparent url( ../images/default/shadow.png ) repeat-x 0 -6px;
}

.loading-indicator {
    font-size: 11px;
    background-image: url( '../images/default/grid/loading.gif' );
    background-repeat: no-repeat;
    background-position: left;
    padding-left: 20px;
    line-height: 16px;
    margin: 3px;
}

.x-text-resize {
    position: absolute;
    left: -1000px;
    top: -1000px;
    visibility: hidden;
    zoom: 1;
}

.x-drag-overlay {
    width: 100%;
    height: 100%;
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    background: white;
    z-index: 20000;
    -moz-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
}

.x-clear {
    clear:both;
    height:0;
    overflow:hidden;
    line-height:0;
    font-size:0;
}
