/*
 * Ext JS Library 1.1 Beta 1
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

.x-menu {
	border:1px solid #718bb7;
    z-index: 15000;
	background: #fff url(../images/default/menu/menu.gif) repeat-y;
}
.ext-ie .x-menu {
    zoom:1;
    overflow:hidden;
}
.x-menu-list{
    border:1px solid #fff;
}
.x-menu li{
	line-height:100%;
}
.x-menu li.x-menu-sep-li{
	font-size:1px;
	line-height:1px;
}
.x-menu-list-item{
	font:normal 11px tahoma,arial, sans-serif;
    white-space: nowrap;
    -moz-user-select: none;
	-khtml-user-select: none;
	display:block;
	padding:1px;
}
.x-menu-item-arrow{
	background:transparent url(../images/default/menu/menu-parent.gif) no-repeat right;
}
.x-menu-sep {
	display:block;
	font-size:1px;
	line-height:1px;
	background:#c3daf9;
	margin: 3px 3px 3px 32px;
}
.x-menu-focus {
	position:absolute;
	left:0;
	top:-5px;
	width:0;
	height:0;
    line-height:1px;
}
.x-menu-item {
	display:block;
	line-height:14px;
	padding:3px 21px 3px 3px;
	white-space: nowrap;
	text-decoration:none;
	color:#233d6d;
	-moz-outline: 0 none;
	outline: 0 none;
	cursor:pointer;
}
.x-menu-item-active {
	color:#233d6d;
	background:#c3daf9;
	border:1px solid #8BB8F3;
	padding:0;
}
.x-menu-item-icon {
	border: 0 none;
	height: 16px;
	padding: 0;
	vertical-align: middle;
	width: 16px;
	margin: 0 11px 0 0;
    background-position:center;
}

.x-menu-check-item .x-menu-item-icon{
	background: transparent url(../images/default/menu/unchecked.gif) no-repeat center;
}

.x-menu-item-checked .x-menu-item-icon{
	background-image:url(../images/default/menu/checked.gif);
}
.x-menu-group-item .x-menu-item-icon{
	background: transparent;
}

.x-menu-item-checked .x-menu-group-item .x-menu-item-icon{
    background: transparent url(../images/default/menu/group-checked.gif) no-repeat center;
}

.x-menu-plain {
	background:#fff;
}
.x-menu-date-item{
	padding:0;
}

.x-menu .x-color-palette, .x-menu .x-date-picker{
    margin-left:32px;
	margin-right:4px;
}
.x-menu .x-date-picker{
    border:1px solid #a3bad9;
    margin-top:2px;
    margin-bottom:2px;
}
.x-menu-plain .x-color-palette, .x-menu-plain .x-date-picker{
	margin:0;
    border:0 none;
}
.x-date-menu {
   padding:0 !important;
}
