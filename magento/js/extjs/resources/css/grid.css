/*
 * Ext JS Library 1.1 Beta 1
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

/* Grid2 styles */
.x-grid {
	position:relative;
	overflow:hidden;
    background-color:#fff;
}
.x-grid-scroller {
	overflow:auto;
}
.x-grid-viewport, .x-grid-locked{
	position:absolute;
	left:0; top: 0;
	z-index:2;
	overflow:hidden;
	visibility:hidden;
}
.x-grid-cell-inner, .x-grid-hd-inner{
	overflow:hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
}
.x-grid-hd-row td, .x-grid-row td{
	font:normal 11px arial, tahoma, helvetica, sans-serif;
    line-height:13px;
    white-space: nowrap;
	vertical-align: top;
	-moz-outline: none;
	-moz-user-focus: normal;
}
.x-grid-hd-row td {
    line-height:14px;
}
.x-grid-col {
	border-right: 1px solid #ebebeb;
	border-bottom: 1px solid #ebebeb;
}
/* Locked styles */

.x-grid-locked .x-grid-body td {
	background-color: #FBFDFF;
	border-right: 1px solid #deecfd;
	border-bottom: 1px solid #deecfd !important;
}
.x-grid-locked .x-grid-body td .x-grid-cell-inner {
	border-top:0 none;
}
.x-grid-locked .x-grid-row-alt td{
	background-color: #F5FAFE;
}

.x-grid-locked .x-grid-header table{
    border-right:1px solid transparent;
}
.x-grid-locked .x-grid-body table{
    border-right:1px solid #c3daf9;
}

.x-grid-locked .x-grid-body td .x-grid-cell-inner {
	
}
.x-grid-row {
	cursor: default;
}
.x-grid-row-alt{
	background-color:#f1f1f1;
}
.x-grid-row-over td{
	background-color:#d9e8fb;
}
.x-grid-resize-proxy {
	width:3px;
	background-color:#cccccc;
	cursor: e-resize;
	cursor: col-resize;
	position:absolute;
	top:0;
	height:100px;
	overflow:hidden;
	visibility:hidden;
	border:0 none;
	z-index:7;
}
.x-grid-focus {
	position:absolute;
	top:0;
	-moz-outline:0 none;
    outline:0 none;
    -moz-user-select: normal;
    -khtml-user-select: normal;
}

/* header styles */
.x-grid-header{
	background: #ebeadb url(../images/default/grid/grid-hrow.gif) repeat-x;
	overflow:hidden;
	position:relative;
	cursor:default;
	width:100%;
}
.x-grid-hd-row{
	height:22px;
}
.x-grid-hd {
	padding-right:1px;
}
.x-grid-hd-over .x-grid-hd-inner {
	border-bottom: 1px solid #c3daf9;
}
.x-grid-hd-over .x-grid-hd-text {
	background: #fafafa url(../images/default/grid/grid-hrow.gif) repeat-x 0 1px;
	padding-bottom:1px;
    border-bottom: 1px solid #b3cae9;
}
.x-grid-sort-icon{
	background-repeat: no-repeat;
	display: none;
	height: 4px;
	width: 13px;
	margin-left:3px;
	vertical-align: middle;
}
.x-grid-header .sort-asc .x-grid-sort-icon {
	background-image: url(../images/default/grid/sort_asc.gif);
	display: inline;
}
.x-grid-header .sort-desc .x-grid-sort-icon {
	background-image: url(../images/default/grid/sort_desc.gif);
	display: inline;
}

/* Body Styles */
.x-grid-body {
	overflow:hidden;
	position:relative;
	width:100%;
	zoom:1;
}

.x-grid-cell-text,.x-grid-hd-text {
	display: block;
	padding: 3px 5px 3px 5px;
	-moz-user-select: none;
	-khtml-user-select: none;
	color:black;
}
.x-grid-hd-text {
    padding-top:4px;
}
.x-grid-split {
	background-image: url(../images/default/grid/grid-split.gif);
	background-position: center;
	background-repeat: no-repeat;
	cursor: e-resize;
	cursor: col-resize;
	display: block;
	font-size: 1px;
	height: 16px;
	overflow: hidden;
	position: absolute;
	top: 2px;
	width: 6px;
	z-index: 3;
}

.x-grid-hd-text {
	color:#15428b;
}
/* Column Reorder DD */
.x-dd-drag-proxy .x-grid-hd-inner{
	background: #ebeadb url(../images/default/grid/grid-hrow.gif) repeat-x;
	height:22px;
	width:120px;
}

.col-move-top, .col-move-bottom{
	width:9px;
	height:9px;
	position:absolute;
	top:0;
	line-height:1px;
	font-size:1px;
	overflow:hidden;
	visibility:hidden;
	z-index:20000;
}
.col-move-top{
	background:transparent url(../images/default/grid/col-move-top.gif) no-repeat left top;
}
.col-move-bottom{
	background:transparent url(../images/default/grid/col-move-bottom.gif) no-repeat left top;
}

/* Selection Styles */
.x-grid-row-selected td, .x-grid-locked .x-grid-row-selected td{
	background-color: #316ac5 !important;
	color: white;
}
.x-grid-row-selected span, .x-grid-row-selected b, .x-grid-row-selected div, .x-grid-row-selected strong, .x-grid-row-selected i{
	color: white !important;
}
.x-grid-row-selected .x-grid-cell-text{
	color: white;
}
.x-grid-cell-selected{
	background-color: #316ac5 !important;
	color: white;
}
.x-grid-cell-selected span{
	color: white !important;
}
.x-grid-cell-selected .x-grid-cell-text{
	color: white;
}

.x-grid-locked td.x-grid-row-marker, .x-grid-locked .x-grid-row-selected td.x-grid-row-marker{
    background: #ebeadb url(../images/default/grid/grid-hrow.gif) repeat-x 0 bottom !important;
    vertical-align:middle !important;
    color:black;
    padding:0;
    border-top:1px solid white;
    border-bottom:none !important;
    border-right:1px solid #6fa0df !important;
    text-align:center;
}
.x-grid-locked td.x-grid-row-marker div, .x-grid-locked .x-grid-row-selected td.x-grid-row-marker div{
    padding:0 4px;
    color:#15428b !important;
    text-align:center;
}

/* dirty cells */
.x-grid-dirty-cell {
    background: transparent url(../images/default/grid/dirty.gif) no-repeat 0 0;
}

/* Grid Toolbars */
.x-grid-topbar, .x-grid-bottombar{
	font:normal 11px arial, tahoma, helvetica, sans-serif;
    overflow:hidden;
	display:none;
	zoom:1;
    position:relative;
}
.x-grid-topbar .x-toolbar{
	border-right:0 none;
}
.x-grid-bottombar .x-toolbar{
	border-right:0 none;
	border-bottom:0 none;
	border-top:1px solid #a9bfd3;
}
/* Props Grid Styles */
.x-props-grid .x-grid-cell-selected .x-grid-cell-text{
	 background-color: #316ac5 !important;
}
.x-props-grid .x-grid-col-value .x-grid-cell-text{
	background-color: white;
}
.x-props-grid .x-grid-col-name{
	 background-color: #c3daf9;
}
.x-props-grid .x-grid-col-name .x-grid-cell-text{
	background-color: white;
    margin-left:10px;
}
.x-props-grid .x-grid-split-value {
    visibility:hidden;
}

/* header menu */
.xg-hmenu-sort-asc .x-menu-item-icon{
	background-image: url(../images/default/grid/hmenu-asc.gif);
}
.xg-hmenu-sort-desc .x-menu-item-icon{
	background-image: url(../images/default/grid/hmenu-desc.gif);
}
.xg-hmenu-lock .x-menu-item-icon{
	background-image: url(../images/default/grid/hmenu-lock.gif);
}
.xg-hmenu-unlock .x-menu-item-icon{
	background-image: url(../images/default/grid/hmenu-unlock.gif);
}

/* dd */
.x-dd-drag-ghost .x-grid-dd-wrap {
    padding:1px 3px 3px 1px;
}