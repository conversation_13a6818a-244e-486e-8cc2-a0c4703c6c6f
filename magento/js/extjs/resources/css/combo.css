/*
 * Ext JS Library 1.1 Beta 1
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

.x-combo-list {
    border:1px solid #98c0f4;
    background:#ddecfe;
    zoom:1;
    overflow:hidden;
}
.x-combo-list-inner {
    overflow:auto;
    background:white;
    position:relative; /* for calculating scroll offsets */
    zoom:1;
    overflow-x:hidden;
}
.x-combo-list-hd {
    font:bold 11px tahoma, arial, helvetica, sans-serif;
    color:#15428b;
    background-image: url(../images/default/layout/panel-title-light-bg.gif);
    border-bottom:1px solid #98c0f4;
    padding:3px;
}
.x-resizable-pinned .x-combo-list-inner {
    border-bottom:1px solid #98c0f4;
}
.x-combo-list-item {
    font:normal 12px tahoma, arial, helvetica, sans-serif;
    padding:2px;
    border:1px solid #fff;
    white-space: nowrap;
    overflow:hidden;
    text-overflow: ellipsis;
}
.x-combo-list .x-combo-selected{
	background-color: #c3daf9 !important;
    cursor:pointer;
    border:1px solid #336699;
}
.x-combo-noedit{
    cursor:pointer;
}