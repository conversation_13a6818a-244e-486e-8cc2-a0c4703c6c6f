/*
 * Ext JS Library 1.1 Beta 1
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

/*
 Creates rounded, raised boxes like on the Ext website - the markup isn't pretty:
  <div class="x-box-blue">
        <div class="x-box-tl"><div class="x-box-tr"><div class="x-box-tc"></div></div></div>
        <div class="x-box-ml"><div class="x-box-mr"><div class="x-box-mc">
            <h3>YOUR TITLE HERE (optional)</h3>
            <div>YOUR CONTENT HERE</div>
        </div></div></div>
        <div class="x-box-bl"><div class="x-box-br"><div class="x-box-bc"></div></div></div>
    </div>
 */

.x-box-tl {
	background: transparent url(../images/default/box/corners.gif) no-repeat 0 0;
    zoom:1;
}

.x-box-tc {
	height: 8px;
	background: transparent url(../images/default/box/tb.gif) repeat-x 0 0;
	overflow: hidden;
}

.x-box-tr {
	background: transparent url(../images/default/box/corners.gif) no-repeat right -8px;
}

.x-box-ml {
	background: transparent url(../images/default/box/l.gif) repeat-y 0;
	padding-left: 4px;
	overflow: hidden;
    zoom:1;
}

.x-box-mc {
	background: #eee url(../images/default/box/tb.gif) repeat-x 0 -16px;
	padding: 4px 10px;
	font-family: "Myriad Pro","Myriad Web","Tahoma","Helvetica","Arial",sans-serif;
	color: #393939;
	font-size: 12px;
}

.x-box-mc h3 {
	font-size: 14px;
	font-weight: bold;
	margin: 0 0 4 0;
    zoom:1;
}

.x-box-mr {
	background: transparent url(../images/default/box/r.gif) repeat-y right;
	padding-right: 4px;
	overflow: hidden;
}

.x-box-bl {
	background: transparent url(../images/default/box/corners.gif) no-repeat 0 -16px;
    zoom:1;
}

.x-box-bc {
	background: transparent url(../images/default/box/tb.gif) repeat-x 0 -8px;
	height: 8px;
	overflow: hidden;
}

.x-box-br {
	background: transparent url(../images/default/box/corners.gif) no-repeat right -24px;
}

.x-box-tl, .x-box-bl {
	padding-left: 8px;
	overflow: hidden;
}

.x-box-tr, .x-box-br {
	padding-right: 8px;
	overflow: hidden;
}

.x-box-blue .x-box-bl, .x-box-blue .x-box-br, .x-box-blue .x-box-tl, .x-box-blue .x-box-tr {
	background-image: url(../images/default/box/corners-blue.gif);
}

.x-box-blue .x-box-bc, .x-box-blue .x-box-mc, .x-box-blue .x-box-tc {
	background-image: url(../images/default/box/tb-blue.gif);
}

.x-box-blue .x-box-mc {
	background-color: #c3daf9;
}

.x-box-blue .x-box-mc h3 {
	color: #17385b;
}

.x-box-blue .x-box-ml {
	background-image: url(../images/default/box/l-blue.gif);
}

.x-box-blue .x-box-mr {
	background-image: url(../images/default/box/r-blue.gif);
}