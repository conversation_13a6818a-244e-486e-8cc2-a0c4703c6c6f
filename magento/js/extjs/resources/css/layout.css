/*
 * Ext JS Library 1.1 Beta 1
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

.x-layout-container{
    width:100%;
    height:100%;
    overflow:hidden;
	 background-color:#c3daf9;
}
.x-layout-container .x-layout-tabs-body{
	border:0 none;
}
.x-layout-collapsed{
    position:absolute;
    left:-10000px;
    top:-10000px;
    visibility:hidden;
    background-color:#c3daf9;
    width:20px;
    height:20px;
    overflow:hidden;
	border:1px solid #98c0f4;
	z-index:20;
}
.ext-border-box .x-layout-collapsed{
    width:22px;
    height:22px;
}
.x-layout-collapsed-over{
    cursor:pointer;
	 background-color:#d9e8fb;
}
.x-layout-collapsed-west .x-layout-collapsed-tools, .x-layout-collapsed-east .x-layout-collapsed-tools{
	position:absolute;
    top:0;
    left:0;
    width:20px;
    height:20px;
}
.x-layout-collapsed-north .x-layout-collapsed-tools, .x-layout-collapsed-south .x-layout-collapsed-tools{
	position:absolute;
    top:0;
    right:0;
    width:20px;
    height:20px;
}
.x-layout-collapsed .x-layout-tools-button{
    margin:0;
}
.x-layout-collapsed .x-layout-tools-button-inner{
    width:16px;
    height:16px;
}
.x-layout-inactive-content{
    position:absolute;
    left:-10000px;
    top:-10000px;
    visibility:hidden;
}
.x-layout-active-content{
    visibility:visible;
}
.x-layout-panel{
    position:absolute;border:1px solid #98c0f4;overflow:hidden;background-color:white;
}
.x-layout-panel-east, .x-layout-panel-west {
    z-index:10;
}
.x-layout-panel-north, .x-layout-panel-south {
    z-index:11;
}
.x-layout-collapsed-north, .x-layout-collapsed-south, .x-layout-collapsed-east, .x-layout-collapsed-west {
    z-index:12;
}
.x-layout-panel-body{
    overflow:hidden;
}
.x-layout-grid-wrapper{

}
.x-layout-split{
    position:absolute;
    height:5px;
    width:5px;
    line-height:1px;
    font-size:1px;
    z-index:3;
    background-color:#c3daf9;
}
.x-layout-panel-hd{
    background-image: url(../images/default/layout/panel-title-light-bg.gif);
    color: black;
    border-bottom:1px solid #98c0f4;
    position:relative;
}
.x-layout-panel-hd-text{
    font:normal 11px tahoma, verdana, helvetica;
    padding: 4px;
    padding-left: 4px;
    display:block;
	 white-space:nowrap;
}
.x-layout-panel-hd-tools{
    position:absolute;
    right:0;
    top:0;
    text-align:right;
    padding-top:2px;
    padding-right:2px;
    width:60px;
}
.x-layout-tools-button{
    z-index:6;
    padding:2px;
    cursor:pointer;
    float:right;
}
.x-layout-tools-button-over{
    padding:1px;
    border:1px solid #98c0f4;
    background-color:white;
}
.x-layout-tools-button-inner{
    height:12px;
    width:12px;
    line-height:1px;
    font-size:1px;
    background-repeat:no-repeat;
    background-position:center;
}
.x-layout-close{
    background-image:url(../images/default/layout/panel-close.gif);
}
.x-layout-stick{
    background-image:url(../images/default/layout/stick.gif);
}
.x-layout-collapse-west,.x-layout-expand-east{
    background-image:url(../images/default/layout/collapse.gif);
}
.x-layout-expand-west,.x-layout-collapse-east{
    background-image:url(../images/default/layout/expand.gif);
}
.x-layout-collapse-north,.x-layout-expand-south{
    background-image:url(../images/default/layout/ns-collapse.gif);
}
.x-layout-expand-north,.x-layout-collapse-south{
    background-image:url(../images/default/layout/ns-expand.gif);
}
.x-layout-split-h{
    background-image:url(../images/default/sizer/e-handle.gif);
    background-position: left;
}
.x-layout-split-v{
    background-image:url(../images/default/sizer/s-handle.gif);
    background-position: top;
}
.x-layout-panel .x-tabs-wrap{
    background:url(../images/default/layout/gradient-bg.gif);
}
.x-layout-panel .x-tabs-body {
    background-color:white;
    overflow:auto;height:100%;
}
.x-layout-component-panel, .x-layout-nested-layout {
	position:relative;
   padding:0;
	overflow:hidden;
	width:200px;
	height:200px;
}
.x-layout-nested-layout .x-layout-panel {
	  border:0 none;
}
.x-layout-nested-layout .x-layout-panel-north {
	  border-bottom:1px solid #98c0f4;
}
.x-layout-nested-layout .x-layout-panel-south {
	  border-top:1px solid #98c0f4;
}
.x-layout-nested-layout .x-layout-panel-east {
	  border-left:1px solid #98c0f4;
}
.x-layout-nested-layout .x-layout-panel-west {
	  border-right:1px solid #98c0f4;
}

.x-layout-panel-dragover {
	border: 2px solid #6593cf;
}
.x-layout-panel-proxy {
	background-image: url(../images/default/layout/gradient-bg.gif);
	background-color:#c3daf9;
	border:1px dashed #6593cf;
	z-index:10001;
	overflow:hidden;
	position:absolute;
	left:0;top:0;
}
.x-layout-slider {
	z-index:15;
	overflow:hidden;
	position:absolute;
}

.x-scroller-up, .x-scroller-down {
	background-color:#c3daf9;
	border: 1px solid #6593cf;
	border-top-color: #fff;
	border-left-color: #fff;
	border-right:0 none;
	cursor:pointer;
	overflow:hidden;
	line-height:16px;
}
.x-scroller-down {
	border-bottom: 0 none;
	border-top: 1px solid #6593cf;
}
.x-scroller-btn-over {
	background-color: #d9e8f8;
}
.x-scroller-btn-click {
	background-color: #AECEF7;
}
.x-scroller-btn-disabled {
	cursor:default;
	background-color: #c3daf9;
	-moz-opacity: 0.3;
   opacity:.30;
   filter: alpha(opacity=30);
}

/* Reader Layout */

.x-reader .x-layout-panel-north {
    border:0 none;
}
.x-reader .x-layout-panel-center{
    border:0 none;
}
.x-reader .x-layout-nested-layout .x-layout-panel-center{
    border:1px solid #99bbe8;
    border-top:0 none;
}
.x-reader .x-layout-nested-layout .x-layout-panel-south{
    border:1px solid #99bbe8;
}