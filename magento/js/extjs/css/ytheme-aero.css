/*
 * Ext JS Library 2.0
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

/* menus */
.x-menu {
	border: 1px solid #718bb7;
	z-index: 15000;
	zoom: 1;
	background: #f0f0f0 url(../images/aero/menu/menu.gif) repeat-y;
	padding: 2px;
}

.x-menu-list{
	background:transparent;
	border:0 none;
}

.x-menu-item-icon {
	margin-right: 8px;
}

.x-menu-sep {
	background-color:#e0e0e0;
	border-bottom:1px solid #fff;
	margin-left:3px;
}
.x-menu-item {
	color:#222;
}
.x-menu-item-active {
	color: #233d6d;
	background: #ebf3fd url(../images/aero/menu/item-over.gif) repeat-x left bottom;
	border:1px solid #aaccf6;
	padding: 0;
}

.x-date-mmenu .x-menu-list{
	padding: 0;
}

.x-date-mmenu .x-menu-list{
	border: 0 none;
}

.x-menu .x-color-palette, .x-menu .x-date-picker{
	margin-left: 26px;
}

.x-menu-plain .x-color-palette, .x-menu-plain .x-date-picker{
	margin: 0;
	border: 0 none;
}


.x-menu-check-item .x-menu-item-icon{
	background-image: url(../images/aero/menu/unchecked.gif);
}

.x-menu-item-checked .x-menu-item-icon{
	background-image:url(../images/aero/menu/checked.gif);
}

.x-menu-group-item .x-menu-item-icon{
	background: transparent;
}

.x-menu-item-checked .x-menu-group-item .x-menu-item-icon{
    background: transparent url(../images/default/menu/group-checked.gif) no-repeat center;
}
/**
* Tabs
*/
.x-tabs-wrap, .x-layout-panel .x-tabs-top .x-tabs-wrap {
	background: #deecfd;
	border: 1px solid #8db2e3;
	padding-bottom: 2px;
	padding-top: 0;
}

.x-tabs-strip-wrap{
	padding-top: 1px;
	background: url(../images/aero/tabs/tab-strip-bg.gif) #cedff5 repeat-x bottom;
	border-bottom: 1px solid #8db2e3;
}

.x-tabs-strip .x-tabs-text {
	color: #15428b;
	font: bold 11px tahoma,arial,verdana,sans-serif;
}

.x-tabs-strip .on .x-tabs-text {
	cursor: default;
	color: #15428b;
}

.x-tabs-top .x-tabs-strip .on .x-tabs-right {
	background: url(../images/aero/tabs/tab-sprite.gif) no-repeat right 0;
}

.x-tabs-top .x-tabs-strip .on .x-tabs-left,.x-tabs-top .x-tabs-strip .on a:hover .x-tabs-left{
	background: url(../images/aero/tabs/tab-sprite.gif) no-repeat 0 -100px;
}

.x-tabs-top .x-tabs-strip .x-tabs-right {
	background: transparent url(../images/aero/tabs/tab-sprite.gif) no-repeat right -50px;
}

.x-tabs-top .x-tabs-strip .x-tabs-left {
	background: transparent url(../images/aero/tabs/tab-sprite.gif) no-repeat 0 -150px;
}

.x-tabs-top .x-tabs-body {
	border: 1px solid #8db2e3;
	border-top: 0 none;
}

.x-tabs-bottom .x-tabs-wrap, .x-layout-panel .x-tabs-bottom .x-tabs-wrap {
	background: #deecfd;
	border: 1px solid #8db2e3;
	padding-top: 2px;
	padding-bottom: 0;
}

.x-tabs-bottom .x-tabs-strip-wrap{
	padding-top: 0;
	padding-bottom: 1px;
	background: url(../images/aero/tabs/tab-strip-btm-bg.gif) #cedff5 repeat-x top;
	border-top: 1px solid #8db2e3;
	border-bottom: 0 none;
}

.x-tabs-bottom .x-tabs-strip .x-tabs-right {
	background: transparent url(../images/aero/tabs/tab-btm-inactive-right-bg.gif) no-repeat bottom right;
}

.x-tabs-bottom .x-tabs-strip .x-tabs-left {
	background: transparent url(../images/aero/tabs/tab-btm-inactive-left-bg.gif) no-repeat bottom left;
}

.x-tabs-bottom .x-tabs-strip .on .x-tabs-right,.x-tabs-bottom .x-tabs-strip .on a:hover {
	background: url(../images/aero/tabs/tab-btm-right-bg.gif) no-repeat bottom left;
}

.x-tabs-bottom .x-tabs-strip .on .x-tabs-left,.x-tabs-bottom .x-tabs-strip .on a:hover .x-tabs-left {
	background: url(../images/aero/tabs/tab-btm-left-bg.gif) no-repeat bottom right;
}

.x-tabs-bottom .x-tabs-body {
	border: 1px solid #8db2e3;
	border-bottom: 0 none;
}

/*
* Basic-Dialog 
*/
.x-dlg-proxy {
	background: #c7dffc;
	border: 1px solid #a5ccf9;
}

.x-dlg-shadow{
	background: #cccccc;
	opacity: .3;
	-moz-opacity: .3;
	filter: alpha(opacity=30);
}

.x-dlg {
	background: transparent;
}

.x-dlg .x-dlg-hd {
	background: url(../images/aero/basic-dialog/hd-sprite.gif) repeat-x 0 -82px;
	background-color: #aabaca;
	color: #15428b;
	zoom: 1;
	padding-top: 7px;
}

.x-dlg .x-dlg-hd-left {
	opacity: .85;
	-moz-opacity: .85;
	filter: alpha(opacity=80);
	background: url(../images/aero/basic-dialog/hd-sprite.gif) no-repeat 0 -41px;
	zoom: 1;
}

.x-dlg-modal .x-dlg-hd-left {
	opacity: .75;
	-moz-opacity: .75;
	filter: alpha(opacity=70);
}

.x-dlg .x-dlg-hd-right {
	background: url(../images/aero/basic-dialog/hd-sprite.gif) no-repeat right 0;
	zoom: 1;
}

.x-dlg .x-dlg-dlg-body{
	padding: 0 0 0;
	position: absolute;
	top: 24px;
	left: 0;
	z-index: 1;
	border: 0 none;
	background: transparent;
}

.x-dlg .x-dlg-bd{
	background: #ffffff;
	border: 1px solid #96b9e6;
}

.x-dlg .x-dlg-ft{
	border: 0 none;
	background: transparent;
	padding-bottom: 8px;
}

.x-dlg .x-dlg-bg{
	filter: alpha(opacity=80);
	opacity: .85;
	-moz-opacity: .85;
	zoom: 1;
}

.x-dlg-modal .x-dlg-bg {
	opacity: .75;
	-moz-opacity: .75;
	filter: alpha(opacity=70);
}

.x-dlg .x-dlg-bg-center {
	padding: 2px 7px 7px 7px;
	background: transparent url(../images/aero/basic-dialog/bg-center.gif) repeat-x bottom;
	zoom: 1;
}

.x-dlg .x-dlg-bg-left{
	padding-left: 7px;
	background: transparent url(../images/aero/basic-dialog/bg-left.gif) no-repeat bottom left;
	zoom: 1;
}

.x-dlg .x-dlg-bg-right{
	padding-right: 7px;
	background: transparent url(../images/aero/basic-dialog/bg-right.gif) no-repeat bottom right;
	zoom: 1;
}

.x-dlg-auto-tabs .x-dlg-dlg-body, .x-dlg-auto-layout .x-dlg-dlg-body{
	background: transparent;
	border: 0 none;
}

.x-dlg-auto-tabs .x-dlg-bd, .x-dlg-auto-layout .x-dlg-bd{
	background: #ffffff;
	border: 1px solid #e9f3f5;
}

.x-dlg-auto-tabs .x-tabs-top .x-tabs-body,.x-dlg-auto-tabs .x-tabs-bottom .x-tabs-body{
	border-color: #8db2e3;
}

.x-dlg-auto-tabs .x-tabs-top .x-tabs-wrap,.x-dlg-auto-tabs .x-tabs-bottom .x-tabs-wrap{
	border-color: #8db2e3;
}

.x-dlg .x-dlg-toolbox {
	width: 50px;
	height: 20px;
	right: 5px;
	top: 5px;
}

.x-dlg .x-dlg-close, .x-dlg .x-dlg-collapse {
	width: 21px;
	height: 20px;
	margin: 0;
}

.x-dlg .x-dlg-close {
	background-image: url(../images/aero/basic-dialog/aero-close.gif);
}

.x-dlg .x-dlg-collapse {
	background-image: url(../images/aero/basic-dialog/collapse.gif);
}

.x-dlg-collapsed {
	border-bottom: 1px solid #96b9e6;
}

.x-dlg .x-dlg-close-over {
	background-image: url(../images/aero/basic-dialog/aero-close-over.gif);
}

.x-dlg .x-dlg-collapse-over {
	background-image: url(../images/aero/basic-dialog/collapse-over.gif);
}

.x-dlg-collapsed .x-dlg-collapse {
	background-image: url(../images/aero/basic-dialog/expand.gif);
}

.x-dlg-collapsed .x-dlg-collapse-over {
	background-image: url(../images/aero/basic-dialog/expand-over.gif);
}

.x-dlg div.x-resizable-handle-east{
	background-image: url(../images/aero/s.gif);
	border: 0 none;
}

.x-dlg div.x-resizable-handle-south{
	background-image: url(../images/aero/s.gif);
	border: 0 none;
}

.x-dlg div.x-resizable-handle-west{
	background-image: url(../images/aero/s.gif);
	border: 0 none;
}

.x-dlg div.x-resizable-handle-southeast{
	background-image: url(../images/aero/basic-dialog/se-handle.gif);
	background-position: bottom right;
	width: 9px;
	height: 9px;
	border: 0;
	right: 2px;
	bottom: 2px;
}

.x-dlg div.x-resizable-handle-southwest{
	background-image: url(../images/aero/s.gif);
	background-position: top right;
	margin-left: 1px;
	margin-bottom: 1px;
	border: 0;
}

.x-dlg div.x-resizable-handle-north{
	background-image: url(../images/aero/s.gif);
	border: 0 none;
}

#x-msg-box .x-dlg-bd{
	background: #cfe0f5;
	border: 0 none;
}

body.x-masked #x-msg-box .x-dlg-bd, body.x-body-masked #x-msg-box .x-dlg-bd{
	background: #c4d2e3;
	border: 0 none;
}

/* BorderLayout */
.x-layout-container{
	background: #deecfd;
}

.x-layout-collapsed{
	background-color: #deecfd;
	border: 1px solid #99bbe8;
}

.x-layout-collapsed-over{
	background-color: #f5f9fe;
}

.x-layout-panel{
	border: 1px solid #99bbe8;
}

.x-layout-nested-layout .x-layout-panel {
	border: 0 none;
}

.x-layout-split{
	background-color: #deecfd;
}

.x-layout-panel-hd{
	background: url(../images/aero/grid/grid-hrow.gif) #ebeadb repeat-x;
	border-bottom: 1px solid #99bbe8;
}

.x-layout-panel-hd-text {
	color: #15428b;
	font: bold 11px tahoma,arial,verdana,sans-serif;
}

.x-layout-split-h{
	background: #deecfd;
}

.x-layout-split-v{
	background: #deecfd;
}

.x-layout-panel .x-tabs-top .x-tabs-wrap{
	border: 0 none;
	border-bottom: 1px solid #8db2e3;
}

.x-layout-panel .x-tabs-bottom .x-tabs-wrap{
	border: 0 none;
	border-top: 1px solid #8db2e3;
}

.x-layout-nested-layout .x-layout-panel-north {
	border-bottom: 1px solid #99bbe8;
}

.x-layout-nested-layout .x-layout-panel-south {
	border-top: 1px solid #99bbe8;
}

.x-layout-nested-layout .x-layout-panel-east {
	border-left: 1px solid #99bbe8;
}

.x-layout-nested-layout .x-layout-panel-west {
	border-right: 1px solid #99bbe8;
}

.x-layout-panel-dragover {
	border: 2px solid #99bbe8;
}

.x-layout-panel-proxy {
	background-image: url(../images/aero/layout/gradient-bg.gif);
	background-color: #f3f2e7;
	border: 1px dashed #99bbe8;
}

.x-layout-container .x-layout-tabs-body{
	border: 0 none;
}

/** Resizable */
.x-resizable-proxy{
	border: 1px dashed #3b5a82;
}

/* grid */
.x-grid-hd-text {
	color: #15428b;
	font-weight: bold;
}

.x-grid-locked .x-grid-body td {
	background: #fbfdff;
	border-right: 1px solid #deecfd;
	border-bottom: 1px solid #deecfd !important;
}

.x-grid-locked .x-grid-body td .x-grid-cell-inner {
	border-top: 0 none;
}

.x-grid-locked .x-grid-row-alt td{
	background: #f5fafe;
}

.x-grid-locked .x-grid-row-selected td{
	color: #ffffff !important;
	background-color: #316ac5 !important;
}

.x-grid-hd{
	border-bottom: 0;
	background: none;
}

.x-grid-hd-row{
	height: auto;
}

.x-grid-hd-over {
	border-bottom: 0 none;
}

.x-grid-hd-over .x-grid-hd-body{
	background: none;
	border-bottom: 0 none;
}

.x-grid-hd-over .x-grid-hd-body{
	background-color: transparent;
	border-bottom: 0;
}

.x-grid-split {
	background-image: url(../images/aero/grid/grid-blue-split.gif);
}

.x-grid-header{
	background: url(../images/aero/grid/grid-hrow.gif);
	border: 0 none;
	border-bottom: 1px solid #6f99cf;
}

.x-grid-row-alt{
	background-color: #f5f5f5;
}

.x-grid-row-over td, .x-grid-locked .x-grid-row-over td{
	background-color: #d9e8fb;
}

.x-grid-col {
	border-right: 1px solid #eeeeee;
	border-bottom: 1px solid #eeeeee;
}

.x-grid-header .x-grid-hd-inner {
	padding-bottom: 1px;
}

.x-grid-header  .x-grid-hd-text {
	padding-bottom: 3px;
}

.x-grid-hd-over .x-grid-hd-inner {
	border-bottom: 1px solid #316ac5;
	padding-bottom: 0;
}

.x-grid-hd-over .x-grid-hd-text {
	background: #d5e4f5;
	border-bottom: 1px solid #ffffff;
	padding-bottom: 2px;
}

.x-grid-header .sort-asc .x-grid-hd-inner, .x-grid-header .sort-desc .x-grid-hd-inner {
	border-bottom: 1px solid #316ac5;
	padding-bottom: 0;
}

.x-grid-header .sort-asc  .x-grid-hd-text, .x-grid-header .sort-desc .x-grid-hd-text {
	border-bottom: 0 none;
	padding-bottom: 3px;
}

.x-grid-header .sort-asc .x-grid-sort-icon {
	background-image: url(../images/aero/grid/sort_asc.gif);
}

.x-grid-header .sort-desc .x-grid-sort-icon {
	background-image: url(../images/aero/grid/sort_desc.gif);
}

.x-dd-drag-proxy .x-grid-hd-inner{
	background: url(../images/aero/grid/grid-hrow.gif) #ebeadb repeat-x;
	height: 22px;
	width: 120px;
}

.x-grid-locked td.x-grid-row-marker, .x-grid-locked .x-grid-row-selected td.x-grid-row-marker{
	background: url(../images/aero/grid/grid-hrow.gif) #ebeadb repeat-x 0 0 !important;
	vertical-align: middle !important;
	color: #000000;
	padding: 0;
	border-top: 1px solid #ffffff;
	border-bottom: 1px solid #6f99cf !important;
	border-right: 1px solid #6f99cf !important;
	text-align: center;
}

.x-grid-locked td.x-grid-row-marker div, .x-grid-locked .x-grid-row-selected td.x-grid-row-marker div{
	padding: 0 4px;
	color: #15428b !important;
	text-align: center;
}

/** Toolbar */
.x-toolbar{
	padding: 2px 2px 2px 2px;
	background: url(../images/default/toolbar/tb-bg.gif) #d0def0 repeat-x;
}

.x-toolbar .ytb-sep{
	background-image: url(../images/aero/grid/grid-blue-split.gif);
}

.x-toolbar .x-btn-over .x-btn-left{
	background: url(../images/aero/toolbar/tb-btn-sprite.gif) no-repeat 0 0;
}

.x-toolbar .x-btn-over .x-btn-right{
	background: url(../images/aero/toolbar/tb-btn-sprite.gif) no-repeat 0 -21px;
}

.x-toolbar .x-btn-over .x-btn-center{
	background: url(../images/aero/toolbar/tb-btn-sprite.gif) repeat-x 0 -42px;
}

.x-toolbar .x-btn-click .x-btn-left, .x-toolbar .x-btn-pressed .x-btn-left, .x-toolbar .x-btn-menu-active .x-btn-left{
	background: url(../images/aero/toolbar/tb-btn-sprite.gif) no-repeat 0 -63px;
}

.x-toolbar .x-btn-click .x-btn-right, .x-toolbar .x-btn-pressed .x-btn-right, .x-toolbar .x-btn-menu-active .x-btn-right{
	background: url(../images/aero/toolbar/tb-btn-sprite.gif) no-repeat 0 -84px;
}

.x-toolbar .x-btn-click .x-btn-center, .x-toolbar .x-btn-pressed .x-btn-center, .x-toolbar .x-btn-menu-active .x-btn-center{
	background: url(../images/aero/toolbar/tb-btn-sprite.gif) repeat-x 0 -105px;
}

/*************** TABS 2 *****************/
/**
* Tabs
*/
.x-tab-panel-header, .x-tab-panel-footer {
	background: #deecfd;
	border: 1px solid #8db2e3;
}

.x-tab-panel-header {
	background: #deecfd;
	border: 1px solid #8db2e3;
	padding-bottom: 2px;
}

.x-tab-panel-footer {
	background: #deecfd;
	border: 1px solid #8db2e3;
	padding-top: 2px;
}

.x-tab-strip-top{
	padding-top: 1px;
	background: url(../images/aero/tabs/tab-strip-bg.gif) #cedff5 repeat-x bottom;
	border-bottom: 1px solid #8db2e3;
}

.x-tab-strip-bottom{
	padding-bottom: 1px;
	background: url(../images/aero/tabs/tab-strip-btm-bg.gif) #cedff5 repeat-x top;
	border-top: 1px solid #8db2e3;
	border-bottom: 0 none;
}

.x-tab-strip .x-tab-strip-text {
	color: #15428b;
	font: bold 11px tahoma,arial,verdana,sans-serif;
}

.x-tab-strip .x-tab-strip-active .x-tab-text {
	cursor: default;
	color: #15428b;
}

.x-tab-strip-top .x-tab-strip-active .x-tab-right {
	background: url(../images/aero/tabs/tab-sprite.gif) no-repeat right 0;
}

.x-tab-strip-top .x-tab-strip-active .x-tab-left {
	background: url(../images/aero/tabs/tab-sprite.gif) no-repeat 0 -100px;
}

.x-tab-strip-top .x-tab-right {
	background: url(../images/aero/tabs/tab-sprite.gif) no-repeat right -50px;
}

.x-tab-strip-top .x-tab-left {
	background: url(../images/aero/tabs/tab-sprite.gif) no-repeat 0 -150px;
}

.x-tab-strip-bottom .x-tab-right {
	background: url(../images/aero/tabs/tab-btm-inactive-right-bg.gif) no-repeat bottom right;
}

.x-tab-strip-bottom .x-tab-left {
	background: url(../images/aero/tabs/tab-btm-inactive-left-bg.gif) no-repeat bottom left;
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right {
	background: url(../images/aero/tabs/tab-btm-right-bg.gif) no-repeat bottom left;
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-left {
	background: url(../images/aero/tabs/tab-btm-left-bg.gif) no-repeat bottom right;
}

.x-tab-panel-body-top {
	border: 1px solid #8db2e3;
	border-top: 0 none;
}

.x-tab-panel-body-bottom {
	border: 1px solid #8db2e3;
	border-bottom: 0 none;
}
