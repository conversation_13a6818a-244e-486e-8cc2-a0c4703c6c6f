/*
 * Ext JS Library 2.0
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

.x-tip{
	position: absolute;
	top: 0;
    left:0;
    visibility: hidden;
	z-index: 20000;
    border:0 none;
}
.x-tip .x-tip-close{
	background-image: url(../images/default/qtip/close.gif);
	height: 15px;
	float:right;
	width: 15px;
    margin:0 0 2px 2px;
    cursor:pointer;
    display:none;
}
.x-tip .x-tip-top {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat 0 -12px;
	height:6px;
    overflow:hidden;
}
.x-tip .x-tip-top-left {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat 0 0;
	padding-left:6px;
    zoom:1;
}
.x-tip .x-tip-top-right {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat right 0;
	padding-right:6px;
    zoom:1;
}
.x-tip .x-tip-ft {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat 0 -18px;
	height:6px;
    overflow:hidden;
}
.x-tip .x-tip-ft-left {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat 0 -6px;
	padding-left:6px;
    zoom:1;
}
.x-tip .x-tip-ft-right {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat right -6px;
	padding-right:6px;
    zoom:1;
}
.x-tip .x-tip-bd {
    border:0 none;
    font: normal 11px tahoma,arial,helvetica,sans-serif;
}
.x-tip .x-tip-bd-left {
	background: #fff url(../images/default/qtip/tip-sprite.gif) no-repeat 0 -24px;
	padding-left:6px;
    zoom:1;
}
.x-tip .x-tip-bd-right {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat right -24px;
	padding-right:6px;
    zoom:1;
}

.x-tip h3 {
    font: bold 11px tahoma,arial,helvetica,sans-serif;
    margin:0;
    padding:2px 0;
    color:#444;
}
.x-tip .x-tip-bd-inner {
    font: normal 11px tahoma,arial,helvetica,sans-serif;
    margin:0 !important;
    line-height:14px;
    color:#444;
    padding:0;
    float:left;
}


.x-form-invalid-tip {
}

.x-form-invalid-tip .x-tip-top {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-top-left {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-top-right {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-ft {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-ft-left {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-ft-right {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-bd-left {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-bd-right {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-bd .x-tip-bd-inner {
    padding-left:24px;
    background:transparent url(../images/default/form/exclamation.gif) no-repeat 2px 2px;
}
.x-form-invalid-tip .x-tip-bd-inner {
    padding:2px;
}