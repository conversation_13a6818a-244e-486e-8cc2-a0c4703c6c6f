/*
 * Ext JS Library 2.0
 * Copyright(c) 2006-2007, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://www.extjs.com/license
 */

#x-debug-browser .x-tree .x-tree-node a span {
    color:#222297;
    font-size:12px;
    padding-top:2px;
    font-family:"courier","courier new";
    line-height:18px;
}
#x-debug-browser .x-tree a i {
    color:#FF4545;
    font-style:normal;
}
#x-debug-browser .x-tree a em {
    color:#999;
}
#x-debug-browser .x-tree .x-tree-node .x-tree-selected a span{
    background:#c3daf9;
}
#x-debug-browser pre, .x-debug-browser pre xmp {
    font:normal 11px tahoma, arial, helvetica, sans-serif !important;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
}
#x-debug-browser pre {
    display:block;
    padding:5px !important;
    border-bottom:1px solid #eeeeee !important;
}
#x-debug-browser pre xmp {
    padding:0 !important;
    margin:0 !important;
}
#x-debug-console .x-layout-panel-center, #x-debug-inspector .x-layout-panel-center {
      border-right:1px solid #98c0f4;
}
#x-debug-console textarea {
    border: 0 none;
    font-size:12px;
    font-family:"courier","courier new";
    padding-top:4px;
    padding-left:4px;
}
.x-debug-frame {
    background:#eeeeee;
    border:1px dashed #aaaaaa;
}