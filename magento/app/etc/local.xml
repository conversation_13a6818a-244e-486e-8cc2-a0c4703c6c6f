<?xml version="1.0"?>
<config>
    <global>
        <install>
            <date><![CDATA[Mon, 29 Aug 2016 10:14:37 +0000]]></date>
        </install>
        <crypt>
            <key><![CDATA[5d5d8440461ea06d354c51908037f2b3]]></key>
        </crypt>
        <disable_local_modules>false</disable_local_modules>
        <resources>
            <db>
                <table_prefix><![CDATA[]]></table_prefix>
            </db>
            <default_setup>
                <connection>
                    <host><![CDATA[db]]></host>
                    <username><![CDATA[root]]></username>
                    <password><![CDATA[root]]></password>
                    <dbname><![CDATA[magento_vip_watches]]></dbname>
                    <initStatements><![CDATA[SET NAMES utf8]]></initStatements>
                    <model><![CDATA[mysql4]]></model>
                    <type><![CDATA[pdo_mysql]]></type>
                    <pdoType><![CDATA[]]></pdoType>
                    <active>1</active>
                </connection>
            </default_setup>
        </resources>

        <cache>
            <backend>Cm_Cache_Backend_Redis</backend>
            <backend_options>
                <server>cache</server>
                <port>6379</port>
                <persistent></persistent>
                <database>1</database>
                <password></password>
                <force_standalone>0</force_standalone>
                <connect_retries>1</connect_retries>
                <read_timeout>10</read_timeout>
                <automatic_cleaning_factor>0</automatic_cleaning_factor>
                <compress_data>1</compress_data>
                <compress_tags>1</compress_tags>
                <compress_threshold>20480</compress_threshold>
                <compression_lib>gzip</compression_lib>
                <use_lua>0</use_lua>
            </backend_options>
        </cache>

        <fpc>
            <backend>Cm_Cache_Backend_Redis</backend>
            <backend_options>
                <server>cache</server>
                <port>6379</port>
                <persistent></persistent>
                <database>2</database>
                <password></password>
                <force_standalone>1</force_standalone>
                <connect_retries>1</connect_retries>
                <lifetimelimit>86400</lifetimelimit>
                <read_timeout>10</read_timeout>
                <compress_data>1</compress_data>
                <compress_tags>1</compress_tags>
                <compress_data>gzip</compress_data>
            </backend_options>
        </fpc>

        <session_save>db</session_save>
        <redis_session>
            <host>cache</host>
            <port>6379</port>
            <password></password>
            <timeout>2.5</timeout>
            <persistent></persistent>
            <db>3</db>
            <compression_threshold>2048</compression_threshold>
            <compression_lib>gzip</compression_lib>
            <log_level>1</log_level>
            <max_concurrency>6</max_concurrency>
            <break_after_frontend>5</break_after_frontend>
            <fail_after>10</fail_after>
            <break_after_adminhtml>30</break_after_adminhtml>
            <first_lifetime>600</first_lifetime>
            <bot_first_lifetime>60</bot_first_lifetime>
            <bot_lifetime>7200</bot_lifetime>
            <disable_locking>0</disable_locking>
            <min_lifetime>60</min_lifetime>
            <max_lifetime>2592000</max_lifetime>
        </redis_session>
    </global>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <frontName><![CDATA[vipadmin]]></frontName>
                </args>
            </adminhtml>
        </routers>
    </admin>
</config>
