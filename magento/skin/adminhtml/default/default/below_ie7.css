/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* Set min-width */
.wrapper { width:expression(document.body.clientWidth<1004?'1003px':'auto'); zoom:1; }
.wrapper-popup { width:expression(document.body.clientWidth<751?'750px':'auto'); }
.grid tr.filter .range div.date { width:121px; }

/* Set min-height */
.middle { height:450px; }
.icon-head { height:18px ;}
.footer .powered-by { height:19px; }
.simple-container { height:350px; }
.messages li { height:23px; }
#widget_window .magento_content { height:450px !important; overflow:visible !important; }

.main-col { height:450px; zoom:1; }

#nav ul li { background:none !important; padding:0 !important; float:none; width:auto; border-left:1px solid #b6b6b6; border-right:1px solid #b6b6b6; vertical-align:top; }
#nav ul li.active { float:none; width:auto; }
#nav ul { border-bottom:2px solid #b6b6b6; padding-top:0 !important; padding-bottom:0 !important; }
#nav ul ul { border-top:1px solid #b6b6b6; background-image:none; }
#nav ul li a { background-color:#e5edef; }
#nav ul li a:hover  { background-color:#d0dfe2; }
#nav ul,
#nav ul li,
#nav ul a,
#nav ul a span { zoom:1; }

#message-popup-window-mask .flash-window { position:absolute; top:350px; }
#popup-window-mask,
.popup-window-mask { padding:0 27px; }
.product-configure-popup { position:absolute; }

.product-options .options-list li { zoom:1; }

/* Hover Fix */
iframe.hover-fix { position:absolute; left:-1px; top:-1px; z-index:-1; background:transparent; filter:progid:DXImageTransform.Microsoft.Alpha(style=0,opacity=0); }
