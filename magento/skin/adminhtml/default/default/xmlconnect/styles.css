/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
.wrapper { min-width:1150px !important; }
.theme_selector ul,
.theme_selector li {
    margin:0 !important;
    padding:0 !important;
    list-style-type:none !important;
    background: #FFFFFF !important;
    color: black !important;

}
.theme_selector img { border:0; }
.theme_selector { font:11px/14px arial; }


.color-block-heading {
    border-bottom:1px solid #e5e5e5; font-size:12px;
}
.color-block-heading a,
.color-block-heading a:hover {
    background:url('../images/xmlconnect/switch-arrow.gif') 0 4px no-repeat; color:#2d2d2d; display:inline-block; font-weight:bold; text-decoration:none; padding-left:13px;
}
.field_label {
    float:left;display:block;border:1px solid #c6cbc7;width:20px;height:20px;padding:1px;
}
.field_fonts_cl {
border:1px
}
.field_colors_cl {
    border:1px}
.field_advanced_cl {
border:1px}

#field_colors { padding:0 !important; overflow:hidden !important; }
#field_colors td { padding:5px 20px !important; }
#field_colors .hor-scroll { overflow:hidden; }
.theme-select-cont { border-bottom:1px solid #dcdcdc; background:#f0f0f0; margin:-5px -20px 10px; padding:20px; width:500px; }
.theme-select-cont:after { display:block; clear:both; content:"."; font-size:0; line-height:0; height:0; overflow:hidden; }
.theme-select-cont .form-buttons button { margin-left:5px; }
.theme-select-cont .advice-container { font-weight:bold; margin:0 0 10px; }
.theme-selector-title { margin:0 30px 0 0px; }
.reset-theme { border-top:1px solid #dcdcdc; background:#f0f0f0; height:20px; margin:-5px -20px; padding:15px; position:relative; text-align:left; }
.reset-theme button { margin-left:5px; float:right; }

.popup-theme-save { background:#fff; border:1px solid #d6d6d6; position:fixed; top:50%; left:50%; margin:-50px 0 0 -100px; padding:20px; width:200px; z-index:999; -webkit-box-shadow: 0px 3px 5px #ccc; -moz-box-shadow:0px 3px 5px #ccc; box-shadow:0px 3px 5px #ccc; }
.popup-theme-save h3 { background:#6f8992; color:#fff; font-size:12px; padding:2px 10px; position:relative; margin:-21px -21px 20px; }
.popup-theme-save .input-text { display:block; margin:0 0 10px; width:200px; }
.popup-theme-save .validation-advice { margin:-7px 0 10px; }

.istore-buttons-set { float:right; margin:0 0 10px; }

.istore { border:1px solid #eeeeee; border-collapse:separate; border-spacing:0; }
.istore td { background:#f2f2f2; color:#000; line-height:18px; padding:5px 7px; }
.istore td label { cursor:pointer; }
.istore .odd td { background:#fafafa; }
.istore .border { border-right:1px solid #dbdbdb; }
.istore .border + td { border-left:1px solid #fff; }

.androidmarket { border-collapse:collapse; border-spacing:0; }
.androidmarket td { padding:2px; }
