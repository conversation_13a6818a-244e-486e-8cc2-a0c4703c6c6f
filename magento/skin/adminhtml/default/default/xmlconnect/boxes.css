/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

.mm_ctrl .m-arrows { width:42px; height:17px; text-indent:-2000em; background:url(../images/xmlconnect/r-arrow.gif) no-repeat 0 0; border:0; float:left; cursor:pointer; }
.mm_ctrl .l-arrow { background:url(../images/xmlconnect/l-arrow.gif) no-repeat 0 0; margin:0 0 0 5px; }
.mm_ctrl .full-arrow { width:84px; background:url(../images/xmlconnect/full-arrow.gif) no-repeat 0 0; margin:0 0 0 5px; }
.mm_ctrl .full-arrow-left { background:url(../images/xmlconnect/full-arrow-left.gif) no-repeat 0 0; width:84px; }
.mm_name span { border:1px solid #bdbdbd; display:block; padding:2px 0; text-align:center; margin:0 7px 5px 7px; }
.mm_img { background: url(../images/xmlconnect/xmlconnect_tab.png) 50% 50% no-repeat; width: 84px; height: 60px; text-align: center; vertical-align: middle }
.mm-edit-form { border:1px solid red; }
.mm-box-blue { border:1px solid #cadcdc; background:#e6eef0; padding:10px 20px; }
.mm-pager { margin:10px 0 0 50px; }
.mm-pager ul, .mm-pager strong, .mm-pager ul li { float:left; }
.mm-pager ul li a { float:left; margin:0 0 0 5px; text-decoration:none; display:block; padding:0 5px; color:#303030; border:1px solid #aaa; }
.mm-pager ul li a.active { border:1px solid #6c8991; background:#6c8991; color:#fff; }
#field_tabs { padding:0 !important; }
#field_tabs .mm-tabs-title { background:#ececec; padding:4px 10px; margin:0 -10px 15px; font-weight:bold; }
#field_tabs table { width:500px !important; margin:0 10px 20px 10px; }

.preview-loading,
.ipad-portrait-loading,
.ipad-landscape-loading { background:url(../images/xmlconnect/loading.gif) no-repeat 50% 40%; color:#d4d4d4; font-size:18px; line-height:467px; text-align:center; }

.preview-loading { line-height:488px; text-align:center; margin:0 0 10px; height:488px; width:328px; }
.ipad-portrait-loading { line-height:467px; }
.ipad-landscape-loading { line-height:350px; }

.ipad-portrait { margin:0 0 5px; overflow:hidden; }
.ipad-landscape { margin:0 0 5px; }
.ipad-tab-landscape { width:425px; }

.save-application { border:solid #d6d6d6; border-width:0 1px 1px; font-weight:bold; margin-bottom:15px; padding:15px; text-align:center; }
.recommended-size { font-size:11px; margin-bottom:15px; padding:0 15px 0 0; }

.app-images { background:#fafafa; border:1px solid #d6d6d6; border-top:none; overflow:hidden; margin:0 0 15px; padding:0 0 0 15px; }
.app-images ul { display:table; zoom:1; }
.item-label { background:#ececec; border-bottom:1px solid #d6d6d6; border-top:1px solid #d6d6d6; clear:left; font-weight:bold; padding:2px 10px; margin:0 0 10px -15px; position:relative; }
.image-item,
.image-item-upload { display:inline-block; text-align:left; margin:0 12px 15px 0; position:relative; vertical-align:top; width:160px; }
.image-item strong { float:left; }
.image-item .edit-btn { background:url(../images/xmlconnect/btn_edit.gif) no-repeat 100% 2px; display:block; overflow:hidden; padding:0 10px 0 5px; }
.image-item .image-placeholder,
.image-item-upload .image-placeholder {
    box-shadow:0 0 5px #000;
    -webkit-box-shadow:0 0 5px #d3d3d3;
       -moz-box-shadow:0 0 5px #d3d3d3;
            box-shadow:0 0 5px #d3d3d3;
    -webkit-box-shadow:0 0 5px rgba(0, 0, 0, .35);
       -moz-box-shadow:0 0 5px rgba(0, 0, 0, .35);
            box-shadow:0 0 5px rgba(0, 0, 0, .35);
    line-height:115px;
    text-align:left;
    height:115px;
    margin:0 0 5px;
    padding-left:25px;
    width:135px;
}

.image-item-upload .banner-image { }
.image-item-upload .flex { position:absolute !important; top:50px; left:100px; }
.image-item-upload .image-placeholder { background:#fff !important; border:1px solid #eee; margin-bottom:0; }

.image-item .uploader .file-row-info,
.image-item-upload .uploader .file-row-info { margin:0; }
.image-item-upload .uploader .file-row { font-size:11px; margin:0; padding:5px; }
.image-item-upload .uploader .file-row .progress-text { float:none; }
.image-item-upload .uploader .file-row-info .file-info { display:none; }
.image-item-upload .uploader .progress,
.image-item-upload .uploader .complete,
.image-item-upload .uploader .error { display:block; height:100px; text-align:center; }
.image-item-upload .uploader .progress,
.image-item-upload .uploader .complete { text-align:center; line-height:95px; }
.image-item-upload .uploader .file-row-info img { vertical-align:bottom; }
.image-item-upload .uploader .file-row-narrow { margin:0; width:140px; }
.image-item .validation-advice { margin:8px 0 0; text-align:left; width:auto; }
.image-item .item-remove { background:url(../images/xmlconnect/remove.png?1) no-repeat 0 0; height:24px; width:24px; position:absolute; top:-12px; right:-12px; text-indent:-999em; }

.banners-wrap { overflow:hidden; position:relative; }
.banners { text-align:center; white-space:nowrap; position:relative; left:0; overflow:hidden; }
.banners img { vertical-align:bottom; }
.banners .gallery-item { float:left; text-align:center; }

#image_edit_block { background:#fafafa; border:1px solid #d6d6d6; position:fixed; top:30%; left:50%; margin:0 0 0 -150px; width:300px; z-index:400; }
.image-edit-popup-head { background:#6f8992; color:#fff; font-weight:bold; padding:2px 10px; }
.image-edit-popup-content { padding:10px 10px 15px; }
.image-edit-popup-content label { vertical-align:middle; }
.image-edit-popup-content .autocomplete { top:21px !important; left:0 !important; }
.image-edit-popup-content .autocomplete ul { overflow:auto; max-height:300px; }
.image-edit-popup-content .action-select { border-bottom:1px solid #ddd; padding:0 0 10px; margin:0 0 10px; }
.image-edit-popup-content .autocomplete-indicator { position:absolute; top:0px; right:2px; }
.image-edit-popup-content .action { position:relative; }
.image-edit-popup-content .action select,
.image-edit-popup-content .action input { width:98%; }
.image-edit-popup .buttons-set { margin:0; padding:0 10px 10px; text-align:right; }
.image-edit-popup .buttons-set button { margin:0 0 0 5px; }
.image-edit-popup .buttons-set button.delete { float:left; margin:0; }
.image-edit-popup .buttons-set button.delete span { background:none; padding:0; }
