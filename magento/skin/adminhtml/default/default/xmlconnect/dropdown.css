/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
.dropdown { float:left; width:200px; border:1px solid #ccc; position:relative; }
.dropdown a { padding:3px 18px 3px 10px ; display:block; text-decoration:none; color:#000; position:relative; }
.dropdown a:hover { background:#ccc; }
.dropdown a span { right:18px; margin-top:2px; position:absolute; }
.dropdown li.ddtitle a,
.dropdown li.ddtitle a:hover { background:url(../images/xmlconnect/dropdown-arrow.gif) 100% 8px no-repeat; }
.dropdown li.ddlist { background:#fff; left:0; position:absolute; border:1px solid #ccc; border-top:0; width:200px; margin:-1px 0 0 -1px !important; padding:1px 0 0 0; }
.dropdown li { zoom:1; }
/* .accordion dt { background:red !important; } */
