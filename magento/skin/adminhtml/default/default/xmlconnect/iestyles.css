/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

*+html .image-item,
*+html .image-item-upload { display:inline; zoom:1; }

.banners-wrap { height:100%; }
.image-item .image-placeholder,
.image-item-upload .image-placeholder {
    filter:
        progid:DXImageTransform.Microsoft.Shadow(color='#d3d3d3', Direction=45, Strength=3)
        progid:DXImageTransform.Microsoft.Shadow(color='#d3d3d3', Direction=135, Strength=3)
        progid:DXImageTransform.Microsoft.Shadow(color='#d3d3d3', Direction=225, Strength=3)
        progid:DXImageTransform.Microsoft.Shadow(color='#d3d3d3', Direction=315, Strength=3);
    position:relative;
    top:-6px;
    left:-6px;
    zoom:1;
}
