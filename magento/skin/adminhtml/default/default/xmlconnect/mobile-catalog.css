/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

@font-face {
    font-family:'stars';
    src:url('fonts/stars-webfont.eot');
    src:local('O'), url('fonts/stars-webfont.woff') format('woff'), url('fonts/stars-webfont.ttf') format('truetype'), url('fonts/stars-webfont.svg#webfontyMStiDRV') format('svg');
    font-weight:normal;
    font-style:normal;
}

.iphone-catalog .main-frame { background:url(../images/xmlconnect/mobile_preview/mm-frame.gif) 0 0 no-repeat; line-height:1; margin:0 0 10px; padding:4px; }
.iphone-catalog .main-block { width:320px; height:478px; position:relative; overflow:hidden; }
.iphone-catalog .header-sign-1 { font:bold 13px/19px arial !important; color:#e9e9e9; float:left; padding:0 15px 0 6px; }
.iphone-catalog .top-header { height:19px; }
.iphone-catalog .battery { height:16px; width:25px; float:right; margin:-2px 5px 5px 13px; }
.iphone-catalog .volume { height:16px; width:25px; float:left; margin-top:-2px; }
.iphone-catalog .antenna { height:16px; width:18px; float:left; margin-left:6px; }
.iphone-catalog .time { position:relative; left:35px; text-align:center; font:bold 13px/19px arial; color:#e9e9e9; float:left; }
.iphone-catalog .main-header { width:100%; height:44px; color:#f3f3f3; padding:0; position:relative; }
.iphone-catalog .login-btn { float:right; height:30px; width:68px; margin-top:7px; font:bold 11px/30px arial; color:#e9e9e9; }
.iphone-catalog .login-body { float:left; background:url(../images/xmlconnect/mobile_preview/login-btn.png) 0 0 repeat-x; line-height:29px; height:29px; padding:0 8px; }
.iphone-catalog .login-left,
.iphone-catalog .login-right,
.iphone-catalog .login-left-alt { float:left; font:bold 1px/1px arial; width:3px; height:29px; }
.iphone-catalog .login-left { background:url(../images/xmlconnect/mobile_preview/login-btn-left.png) 0 0 no-repeat; }
.iphone-catalog .login-left-alt { background:url(../images/xmlconnect/mobile_preview/login-btn-left-alt.png) 0 0 no-repeat; width:12px; }
.iphone-catalog .login-right { width:4px; background:url(../images/xmlconnect/mobile_preview/login-btn-right.png) 0 0 no-repeat; }
.iphone-catalog .main-header .gradient { height:44px; width:100%; position:absolute; background:url(../images/xmlconnect/mobile_preview/gradients/header2.png) 0 0 no-repeat; left:0; }
.iphone-catalog .header-buttons,
.iphone-catalog .header-buttons td { height:40px !important; padding:0 !important; margin:0 !important; }
.iphone-catalog .info { text-align:center; vertical-align:middle; width:30px; height:42px; }
.iphone-catalog .logo-small { width:165px; height:44px; overflow:hidden; text-align:center; vertical-align:middle; }
.iphone-catalog .logo-small div { width:165px; height:41px; overflow:hidden; text-align:center; vertical-align:middle; }
.iphone-catalog .logo-small img { vertical-align:middle; }
.iphone-catalog .logo-small .sh-title { text-align:left; height:30px; vertical-align:middle; }
.iphone-catalog .logo-small .sh-title1,
.iphone-catalog .logo-small .sh-title2 { font:bold 16px/44px arial !important; color:#000; position:relative; }
.iphone-catalog .logo-small .sh-title1 { display:inline-block; }
.iphone-catalog .logo-small .sh-title2 { color:#fff; position:absolute; top:-1px; left:-1px; }
.iphone-catalog .edit-filter { width:75px; float:left; margin-right:10px; margin-top:0; }
.iphone-catalog .back-button { float:left; margin:0 0 0 7px; width:60px; }
.iphone-catalog .clearB { clear:both; height:1px; }
.iphone-catalog .filter-header { height:22px; text-align:center; vertical-align:middle; }
.iphone-catalog .filter-header .gradient { height:22px; width:100%; position:absolute; background:url(../images/xmlconnect/mobile_preview/gradients/header3.png) 0 0 repeat-x; left:0; }
.iphone-catalog .filter-applied { padding-top:1px; color:#fff; font:bold 10px/18px arial; }
.iphone-catalog .filter-circle { margin-left:99px; display:block; float:left; width:18px; height:18px; background:url(../images/xmlconnect/mobile_preview/circle.png) 0 0 no-repeat; }
.iphone-catalog .filter-text { float:left; padding:0 0 0 5px; font:bold 11px/20px arial; }
.iphone-catalog .filter-lines { float:right; width:25px; height:22px; padding:0 25px 0 0; background:url(../images/xmlconnect/mobile_preview/lines-h.png) 29px 7px no-repeat; }
.iphone-catalog .sort-block { height:42px; text-align:center; }
.iphone-catalog .sort-block-inner { position:relative; height:42px; background:url(../images/xmlconnect/mobile_preview/gradients/sort-bg.png) 0 0 repeat-x !important; }
.iphone-catalog .sort-block-inner-txt { float:left; width:75px; height:26px; padding-top:12px; font:bold 11px/18px arial !important; }
.iphone-catalog .sort-buttons { text-align:center; font:bold 11px/11px Helvetica, "Helvetica Neue", Verdana, sans-serif; padding:0 5px 0 0; width:234px; height:26px; margin-top:8px; }
.iphone-catalog .buttons-holder { float:left; margin:5px 0 0 5px; -moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; -moz-box-shadow:0 1px 1px #bbb; -webkit-box-shadow:0 1px 1px #bbb; box-shadow:0 1px 1px #bbb; }
.iphone-catalog .sort-block .button { background:url(../images/xmlconnect/mobile_preview/sort_buttons/bg_button.png) no-repeat 0 0 #8c8c8c; border:1px solid #606060; display:inline; float:left; color:#fefefe; font:bold 14px/16px Helvetica, "Helvetica Neue", Verdana, sans-serif; padding:6px 9px 5px; text-shadow:0 -1px 1px #222; -moz-box-shadow:inset 0 1px 1px #777; -webkit-box-shadow:inset 0 1px 1px #777; box-shadow:inset 0 1px 1px #777; }
.iphone-catalog .sort-block .button-pos { padding-right:2px; text-shadow:0 -1px 1px #666; -moz-border-radius:5px 0 0 5px; -webkit-border-radius:5px 0 0 5px; border-radius:5px 0 0 5px; }
.iphone-catalog .sort-block .button-name,
.iphone-catalog .sort-block .button-price { padding:6px 19px 5px; }
.iphone-catalog .sort-block .button-name { border-width:1px 0; }
.iphone-catalog .sort-block .button-price { -moz-border-radius:0 5px 5px 0; -webkit-border-radius:0 5px 5px 0; border-radius:0 5px 5px 0; }
.iphone-catalog .sort-block .button img { vertical-align:middle; }
.iphone-catalog .active { color:#e9e9e9; }
.iphone-catalog .inactive {	color:#333; }
.iphone-catalog .item { font-family:Helvetica, "Helvetica Neue", Arial, sans-serif; height:91px; position:relative; }
.iphone-catalog .item .gradient { height:88px; width:100%; position:absolute; bottom:0; background:url(../images/xmlconnect/mobile_preview/gradients/item-bg.png) 0 0 repeat-x; left:0; }
.iphone-catalog .slider { background:url('../images/xmlconnect/mobile_preview/actionsBg.png') left center repeat-x; }
.iphone-catalog .slider-item { width:20%; height:91px;	float:left; text-align:center; }
.iphone-catalog .slider-image { position:relative;	top:20px; }
.iphone-catalog .slider-item-text { position:relative;	top:25px; }
.iphone-catalog .lines-v { height:91px; width:20px; background:url('../images/xmlconnect/mobile_preview/lines-v.png') center center no-repeat; float:left; }
.iphone-catalog .arrow { height:91px; width:20px;	background:url('../images/xmlconnect/mobile_preview/arrow.png') center center no-repeat; float:right; }
.iphone-catalog .item-image { padding:10px 0 0 10px; float:left; }
.iphone-catalog .item-image img { -moz-box-shadow:0 2px 3px #999; -webkit-box-shadow:0 2px 3px #999; box-shadow:0 2px 3px #999; }
.iphone-catalog .item-info { float:left; text-align:left;	padding:10px 0 0 20px; }
.iphone-catalog .item-title { font:bold 12px/15px "Helvetica Neue", Helvetica, Verdana, sans-serif !important; padding:0; }
.iphone-catalog .item-price-block span,
.iphone-catalog .availability span,
.iphone-catalog .slider-item-text { font-family:"Helvetica Neue", Helvetica, Verdana, sans-serif !important; }
.iphone-catalog .slider-item-text { font-size:10px !important; padding:0 5px; }
.iphone-catalog .item-price-block span { font:bold 12px/14px Tahoma, Verdana, Geneva !important; }
.iphone-catalog .item-price-block { padding:2px 0 0; }
.iphone-catalog .availability { padding:5px 0; }
.iphone-catalog .item-rate { padding:4px 0 0; }
.iphone-catalog .item-rate .stars { font-family:stars; font-size:16px; vertical-align:middle; }
.iphone-catalog .item-rate .star { vertical-align:top; }
.iphone-catalog .item-rate strong { font-size:10px; line-height:16px; vertical-align:text-top; }
.iphone-catalog .item-rate {}
.iphone-catalog .bottom-buttons { height:48px; position:absolute; bottom:0; background:#060606; width:320px; }
.iphone-catalog .bottom-buttons .gradient { position:absolute; width:320px; background:url(../images/xmlconnect/mobile_preview/gradients/footer.png) 0 0 no-repeat !important; }
.iphone-catalog .bottom-button { height:26px; vertical-align:top; }
.iphone-catalog .bottom-button p { text-align:center; font:normal 10px/11px arial; color:#9f9f9f; margin:0; padding-top:33px; position:relative; z-index:100; }
.iphone-catalog .bottom-button-active p { color:#fff; margin-top:-46px; font-weight:bold; }
.iphone-catalog .bottom-button-active .background { background:#fff; height:44px; position:relative; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=14)"; filter:alpha(opacity=14); opacity:0.14; -moz-border-radius:2px; -webkit-border-radius:2px; border-radius:2px; }
