/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

.iphone-home .main-frame { background:url(../images/xmlconnect/mobile_preview/mm-frame.gif) 0 0 no-repeat; line-height:1; margin:0 0 10px; padding:4px; }
.iphone-home .main-block { position:relative; height:478px; width:320px; overflow:hidden; }
.iphone-home .header-sign-1 { font:bold 13px/19px arial !important; color:#e9e9e9; float:left; padding:0 15px 0 6px; }
.iphone-home .top-header { height:19px; }
.iphone-home .battery { height:16px;	width:25px;	float:right; margin:-2px 5px 5px 13px; }
.iphone-home .volume { height:16px; width:25px; float:left; margin-top:-2px; }
.iphone-home .antenna { height:16px; width:18px; float:left;	margin-left:6px; }
.iphone-home .time {	position:relative; left:35px; text-align:center; font:bold 13px/19px arial;	color:#e9e9e9; float:left; }
.iphone-home .main-header { width:100%; height:44px; color:#f3f3f3; padding:0; position:relative; }
.iphone-home .main-header .gradient { background:url(../images/xmlconnect/mobile_preview/gradients/header2.png) 0 0 no-repeat; height:44px; width:100%; position:absolute; left:0; }
.iphone-home .clearB { clear:both; height:1px; }
.iphone-home .header-buttons,
.iphone-home .header-buttons td { height:40px !important; padding:0 !important; margin:0 !important; vertical-align:middle; }
.iphone-home .header-buttons .login-btn { padding:5px 0 0 !important; height:35px !important; width:68px; font:bold 11px/30px arial; color:#e9e9e9; }
.iphone-home .info { text-align:left; vertical-align:middle; width:51px; height:42px; }
.iphone-home .info img { margin-left:5px; }
.iphone-home .logo-small { width:200px; height:44px; overflow:hidden; text-align:center; vertical-align:middle; }
.iphone-home .logo-small div { width:200px; height:41px; overflow:hidden; text-align:center; vertical-align:middle; }
.iphone-home .logo-small img { vertical-align:middle; }
.iphone-home .logo-small .sh-title { text-align:left; height:30px; vertical-align:middle; }
.iphone-home .logo-small .sh-title1,
.iphone-home .logo-small .sh-title2 { font:bold 16px/44px arial !important; color:#000; position:relative; }
.iphone-home .logo-small .sh-title1 { display:inline-block; }
.iphone-home .logo-small .sh-title2 { color:#fff; position:absolute; top:-1px; left:-1px; }
.iphone-home .login-body { float:left; background:url(../images/xmlconnect/mobile_preview/login-btn.png) 0 0 repeat-x; line-height:29px; height:29px; padding:0 8px; }
.iphone-home .login-left,
.iphone-home .login-right { float:left; font:bold 1px/1px arial; width:3px; height:29px; }
.iphone-home .login-left { background:url(../images/xmlconnect/mobile_preview/login-btn-left.png) 0 0 no-repeat; }
.iphone-home .login-right { background:url(../images/xmlconnect/mobile_preview/login-btn-right.png) 0 0 no-repeat; width:4px; }
.iphone-home .title { position:relative; top:10px; font:bold 19px arial; color:#e9e9e9; }
.iphone-home .main-header-text { width:10px; }
.iphone-home .big-logo { text-align:center; padding:0; height:230px; width:320px; overflow:hidden; }
.iphone-home .catalog { height:137px; width:500px; position:absolute; padding-left:5px; }
.iphone-home .item { float:left; width:80px; padding:5px 5px 3px 5px; text-align:center;	margin:10px 5px 0 5px; height:112px; position:relative; }
.iphone-home .item img { display:block; margin:0 0 10px 0; }
.iphone-home .item-text { height:21px; text-align:center; margin-top:0px; width:80px; font:bold 12px/21px arial !important; border-radius:6px; -moz-border-radius:6px; -webkit-border-radius:6px; }
.iphone-home .bottom-buttons { height:48px; position:absolute; bottom:0; background:#060606; width:320px; }
.iphone-home .bottom-buttons .gradient { position:absolute; width:320px; background:url(../images/xmlconnect/mobile_preview/gradients/footer.png) 0 0 no-repeat !important; }
.iphone-home .bottom-button { height:26px; vertical-align:top; }
.iphone-home .bottom-button p { text-align:center; font:normal 10px/11px arial; color:#9f9f9f; margin:0; padding-top:33px; position:relative; z-index:100; }
.iphone-home .bottom-button-active p { color:#fff; margin-top:-46px; font-weight:bold; }
.iphone-home .bottom-button-active .background { background:#fff; height:44px; position:relative; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=14)"; filter:alpha(opacity=14); opacity:0.14; -moz-border-radius:2px; -webkit-border-radius:2px; border-radius:2px; }
