/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

 /* Reset ================================================================================= */
* { margin:0; padding:0; }
body          { background:#496778 50% 0 repeat-y; font:12px/1.55 Arial, Helvetica, sans-serif; color:#2f2f2f; text-align:center; }
img           { border:0; vertical-align:top; }
a             { color:#1e7ec8; text-decoration:underline; }
a:hover       { text-decoration:none; }
:focus        { outline:0; }

/* Headings */
h1,h2,h3,
h4,h5,h6      { margin:0 0 5px; line-height:1.35; color:#0a263c; }
h1            { font-size:20px; font-weight:normal; }
h2            { font-size:18px; font-weight:normal; }
h3            { font-size:16px; font-weight:bold; }
h4            { font-size:14px; font-weight:bold; }
h5            { font-size:12px; font-weight:bold; }
h6            { font-size:11px; font-weight:bold; }

/* Forms */
form          { display:inline; }
fieldset      { border:0; }
legend        { display:none; }

/* Table */
table         { border:0; /*border-collapse:collapse;*/ border-spacing:0; empty-cells:show; font-size:100%; }
caption,th,td { vertical-align:top; text-align:left; font-weight:normal; }

/* Content */
p             { margin:0 0 10px; }
strong        { font-weight:bold; }
address       { font-style:normal; line-height:1.35; }
cite          { font-style:normal; }
q,
blockquote    { quotes:none; }
q:before,
q:after       { content:''; }
small,big     { font-size:1em; }
/*sup           { font-size:1em; vertical-align:top; }*/

/* Lists */
ul,ol         { list-style:none; }

/* Tools */
.hidden       { display:block !important; border:0 !important; margin:0 !important; padding:0 !important; font-size:0 !important; line-height:0 !important; width:0 !important; height:0 !important; overflow:hidden !important; }
.nobr         { white-space:nowrap !important; }
.wrap         { white-space:normal !important; }
.a-left       { text-align:left !important; }
.a-center     { text-align:center !important; }
.a-right      { text-align:right !important; }
.v-top        { vertical-align:top; }
.v-middle     { vertical-align:middle; }
.f-left,
.left         { float:left !important; }
.f-right,
.right        { float:right !important; }
.f-none       { float:none !important; }
.f-fix        { float:left; width:100%; }
.no-display   { display:none; }
.no-margin    { margin:0 !important; }
.no-padding   { padding:0 !important; }
.no-bg        { background:none !important; }
/* ======================================================================================= */

/* Default styles ======================================================================== */
/* boxes.css*/
.notification-global {
    background: url("./images/error_msg_icon.gif") no-repeat scroll 27px 5px #FFF9E9;
    border-bottom: 1px solid #EEE2BE;
    border-top: 1px solid #EEE2BE;
    color: #444444;
    font-size: 11px;
    line-height: 16px;
    margin: 0 0 -3px;
    padding: 5px 27px 5px 47px;
    position: relative;
}
.notification-global-notice {
    background-image: url("./images/note_msg_icon.gif");
}
.notification-global .label {
    color: #EB5E00;
}
.notification-global .clickable {
    cursor: pointer;
}
.notification-global span.critical {
    color: #D20000;
}
.notification-global a:hover {
    text-decoration: none;
}
.error, a.error span, .required, .validation-advice {
    color: #D40707 !important;
    font-weight: bold !important;
}
.notice {
    color: #EA7601;
}
.messages ul {
    border: 0 none !important;
}
.messages li {
    font-size: 0.95em !important;
    font-weight: bold !important;
    margin-bottom: 11px !important;
    min-height: 23px !important;
    padding: 8px 8px 2px 32px !important;
}
.messages ul li {
    border: 0 none !important;
    margin: 0 0 3px !important;
    padding: 0 !important;
}
.error-msg {
    background: url("./images/error_msg_icon.gif") no-repeat scroll 10px 10px #FAEBE7 !important;
    border: 1px solid #F16048 !important;
    color: #DF280A !important;
}
.success-msg {
    background: url("./images/success_msg_icon.gif") no-repeat scroll 10px 10px #EFF5EA !important;
    border: 1px solid #95A486 !important;
    color: #3D6611 !important;
}
.notice-msg {
    background: url("./images/note_msg_icon.gif") no-repeat scroll 10px 10px #FFFBF0 !important;
    border: 1px solid #FFD967 !important;
    color: #3D6611 !important;
}
.warning-msg {
    background: url("./images/warning_msg_icon.gif") no-repeat scroll 10px 10px #E6E6E6 !important;
    border: 1px solid #666E73 !important;
    color: #000000 !important;
}

button:hover, .form-button:hover {
    background: url("./images/btn_over_bg.gif") repeat-x scroll 0 0 #F77C16;
}
button:active, .form-button:active {
    background: url("./images/btn_on_bg.gif") repeat-x scroll 0 0 #F77C16;
}
button span {
    background-position: 0 50%;
    background-repeat: no-repeat;
    line-height: 1.35em;
}
button span span {
    background: none repeat scroll 0 0 transparent !important;
    display: inline !important;
    margin: 0 !important;
    padding: 0 !important;
}


input.input-text, textarea, select {
    background: none repeat scroll 0 0 #FFFFFF;
    border-color: #AAAAAA #C8C8C8 #C8C8C8 #AAAAAA;
    border-style: solid;
    border-width: 1px;
    font: 12px arial,helvetica,sans-serif;
}
input.input-text, textarea {
    padding: 2px;
}
button, .form-button {
    background: url("./images/btn_bg.gif") repeat-x scroll 0 100% #FFAC47;
    border-color: #ED6502 #A04300 #A04300 #ED6502;
    border-style: solid;
    border-width: 1px;
    color: #FFFFFF;
    cursor: pointer;
    font: bold 12px arial,helvetica,sans-serif;
    padding: 0 7px 1px;
    text-align: center !important;
    white-space: nowrap;
}
/* ======================================================================================= */


*, html {text-align: left;}

h1 {
    color: #D12C01;
    font-size: 22px;
    font-weight: normal;
    margin: 0;
    text-align: left;
}

h2 {
    color: #444444;
    font: 16px Arial,Helvetica,sans-serif;
    margin: 0 0 5px;
    text-align: left;
}

body,
.main-container,
.header-container {background: none;}

.header-simple {height: 60px; text-align: left; padding: 10px 10px 0 10px; border-bottom: 1px solid #CFCFCF;}
.header-top img {height: 60px;}

.login-box > div {padding: 10px;}
.page-title {margin-bottom: 0; border-bottom: 1px solid #CFCFCF;}

.form-box {border-bottom: 1px solid #CFCFCF;}
.form-box .fieldset, .form-box fieldset {width: 320px; margin: 0 auto;}

.input-box {margin-bottom: 10px;}
.input-box label {width: 95px; font-weight: bold; display: block; float: left; text-align: right; padding-right: 10px;}
.input-box input {width: 209px;}

.required {font-weight: normal; color: #D40707 !important; font-size: 11px;}
.form-buttons {text-align: right;}
