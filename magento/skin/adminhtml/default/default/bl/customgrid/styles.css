/**
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 *
 * @copyright  Copyright (c) 2012 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

/* GLOBAL STYLES */

.blcg-clearer { clear:both; }
.blcg-tooltip { padding:4px 8px; background:#fff; border:1px solid #000; }

/* MESSAGES LIST */

.blcg-messages-wrapper { position:fixed; right:0; bottom:0; width:100%; z-index:10; }
.blcg-messages-tabs { height:32px; margin-right:16px; }
.blcg-messages-tab { float:right; display:block; height:32px; line-height:34px; margin-left:8px; padding:0 10px 0 30px; font-weight:bold; border-style:solid; border-width:1px 1px 0; background-repeat:no-repeat; background-position:6px center; cursor:pointer; }
.blcg-messages-tab.no-display { display:none; }
.blcg-messages-lists { clear:both; }
.blcg-messages-list-wrapper { max-height:200px; overflow-y:auto; border-width:1px 0 0; border-style:solid; }
.blcg-messages-list { padding:8px 12px; }
.blcg-messages-content-date { margin:0 0 4px; font-size:14px; font-weight:bold; }
.blcg-messages-tab-warning { color:#000000; background-color:#e6e6e6; border-color:#666e73; }
.blcg-messages-tab-error,
.blcg-messages-list-wrapper-error { color:#df280a; background-color:#faefe7; border-color:#f16048; }
.blcg-messages-tab-success,
.blcg-messages-list-wrapper-success { color:#3d6611; background-color:#eff5ea; border-color:#95a486; }
.blcg-messages-tab-notice,
.blcg-messages-list-wrapper-notice { color:#3d6611; background-color:#fffbf0; border-color:#ffd967; }
.blcg-messages-tab-warning,
.blcg-messages-list-wrapper-warning { color:#000000; background-color:#e6e6e6; border-color:#666e73; }
.blcg-messages-tab-error { background-image:url(../../images/error_msg_icon.gif); }
.blcg-messages-tab-success { background-image:url(../../images/success_msg_icon.gif); }
.blcg-messages-tab-notice { background-image:url(../../images/note_msg_icon.gif); }
.blcg-messages-tab-warning { background-image:url(../../images/warning_msg_icon.gif); }
.blcg-messages-list li { padding:8px 8px 2px !important; background:none !important; }
.blcg-messages-list ul li { margin:0 !important; }

/* IN-GRID / EDIT BLOCK HEADER */

.blcg-content-header { margin-bottom:18px; padding-top:9px; padding-bottom:.25em; font-family:Arial,Helvetica,sans-serif; border-bottom:4px solid #dfdfdf; }
.blcg-content-header h3 { margin:.3em .5em 0 0; padding:0; line-height:1.2em; font-size:1.25em; color:#eb5e00; }
button.blcg-customize span { padding-left:20px; background-image:url(images/ico_custom.gif); }
button.blcg-grid-infos span { padding-left:20px; background-image:url(images/ico_infos.gif); }
button.blcg-additional span { padding-left:20px; background-image:url(images/ico_arrow_down.gif); }
button.blcg-export span { padding-left:20px; background-image:url(images/ico_export.gif); }
.blcg-custom-grid-config-switch { margin:0 0 6px; text-align:right; }

/* ADDITIONAL FEATURES BLOCK */

.blcg-custom-grid-config-additional-container { width:100%; padding:8px 0; background:#eee; }
.blcg-custom-grid-config-additional-part { position:relative; float:left; min-width:180px; height:132px; margin:4px 6px; padding:2px 2px 22px; background:#fff; border:1px solid #000; }
.blcg-custom-grid-config-additional-part-label { height:20px; padding:0 6px 3px; overflow:hidden; line-height:21px; white-space:nowrap; font-weight:bold; color:#fff; background:url(images/bkg_box_top.jpg) repeat-x; }
.blcg-custom-grid-config-additional-part-label img { padding:0 0 3px; vertical-align:middle; }
.blcg-custom-grid-config-additional-part-content { height:120px; padding:4px 8px; overflow:hidden; background:url(images/bkg_box_content.jpg) repeat; }
.blcg-custom-grid-config-additional-part-content select { width:100%; }
.blcg-custom-grid-config-additional-part-content select option { padding:0 3px; }
.blcg-custom-grid-config-additional-part li { padding:0 0 1px 3px; }
.blcg-custom-grid-config-additional-part li select { width:6em; margin:2px 0; }
.blcg-custom-grid-config-additional-part-actions { position:absolute; left:0; bottom:5px; width:100%; height:22px; overflow:hidden; text-align:center; }
.blcg-pagination-input { width:3em; padding:1px; text-align:center; }

/* COLUMNS LIST */

.blcg-custom-grid-config { margin:0 0 6px; }
.blcg-custom-grid-config .grid table tr:hover,
.blcg-custom-grid-config .grid table tr.dnd_whileDrag { background-color:#fcf5dd; } 
.blcg-custom-grid-config .grid table td { vertical-align:middle; }
.blcg-custom-grid-config .grid table .blcg-drag-handle { padding-left:9px; padding-right:9px;  background:url(images/up-down.gif) no-repeat center center; cursor:move; }
.blcg-custom-grid-config .grid table .blcg-store-select { margin:0; }

/* CONFIG / HELP BUTTONS */

.blcg-attribute-config-button { color:#ea7601; text-decoration:underline; cursor:pointer; }
.blcg-collection-renderers-help-button,
.blcg-collection-renderers-config-button,
.blcg-custom-column-config-button,
.grid tr.filter .blcg-categories-filter .blcg-filter-button { display:inline-block; width:16px; height:16px; zoom:1; vertical-align:middle; cursor:pointer; }
.blcg-collection-renderers-config-button,
.blcg-custom-column-config-button,
.grid tr.filter .blcg-categories-filter .blcg-filter-button { background:url(images/ico_config.png) no-repeat center center; }
.blcg-collection-renderers-help-button { background:url(images/ico_help.png) no-repeat center center; }

/* ADDITIONAL FILTERS */

.blcg-additional-filters-table .filter th { width:150px; }

/* IN-GRID EDITOR */

.grid table td.blcg-column-editor-editing { padding-right:38px; }
.grid table td.blcg-column-editor-updated { background:#ebffd4 !important; }
.grid table td.blcg-column-editor-editing p.note { margin:0; padding:0 0 0 13px; font-size:11px; background:url(images/grid_note_bg.gif) 1px 6px no-repeat; }
.grid td.blcg-column-editor-editing input.input-text,
.grid td.blcg-column-editor-editing select { width:90%; max-width:270px; min-width:110px; }
.grid td.blcg-column-editor-editing select.multiselect { min-width:150px; }
.blcg-column-editor-form { clear:both; }
.blcg-column-editor-form-buttons { float:right; }
.blcg-column-editor-overlay { height:16px; }
.blcg-column-editor-overlay-edit,
.blcg-column-editor-overlay-validate,
.blcg-column-editor-overlay-cancel,
.blcg-column-editor-overlay-updated { float:left; width:16px; height:16px; background-repeat:no-repeat; background-position:center center; cursor:pointer; }
.blcg-column-editor-overlay-edit { background-image:url(images/edit.png); }
.blcg-column-editor-overlay-validate { background-image:url(images/validate.gif); }
.blcg-column-editor-overlay-cancel { background-image:url(images/cancel.gif); }
.blcg-editor-required-marker { color:#f00; }
.entry-edit-head .blcg-editor-required-marker { padding:0 3px 0 2px; font-style:italic; color:#f34; }
.blcg-column-editor-editing .blcg-editor-required-marker { display:none; }
.grid table td.blcg-column-editor-editing-required { background:#ffdcdc !important; }
.blcg-editor-scope-label { font-size:0.9em; }
.adminhtml-blcg-custom-grid-editor-edit { background:#fff; }
.adminhtml-blcg-custom-grid-editor-edit .middle { background:none; min-height:0; }

/* COLUMNS RENDERERS */

.blcg-collection-renderers-descriptions-list { height:370px; max-height:370px; overflow-y:auto; }
.blcg-collection-renderers-descriptions-list dt { font-weight:bold; }
.blcg-collection-renderers-descriptions-list dd { margin:0 0 6px; padding-left:10px; }
.blcg-collection-renderer-help,
.blcg-attribute-renderer-help { margin:0 0 12px; font-style:italic; }
.blcg-custom-grid-forced-renderer span { vertical-align:top; }

/* CUSTOM COLUMNS */

.blcg-custom-columns-wrapper { width:240px; height:92px; overflow-y:auto; }
.blcg-custom-columns-group-label { height:18px; padding:0 0 0 12px; overflow:hidden; font-weight:bold; text-decoration:none; background:url(images/arrow_left.png) no-repeat left center; color:#000; cursor:pointer; }
.blcg-custom-columns-group-label-on { background:url(images/arrow_down.png) no-repeat left 7px; }
.blcg-custom-columns-group-label span { font-weight:normal; }
.blcg-custom-columns-column { position:relative; max-width:194px; height:16px; margin:0 3px 0 0; padding:1px 36px 1px 6px; overflow:hidden; font-size:11px; }
.blcg-custom-columns-column label { display:block; max-width:194px; height:16px; line-height:15px; overflow:hidden; cursor:pointer; }
.blcg-custom-columns-column-buttons { position:absolute; top:0; right:0; }
.blcg-custom-columns-column-button { float:left; position:relative; width:14px; height:14px; margin:1px 0 1px 4px; overflow:hidden; cursor:pointer; }
.blcg-custom-columns-column-button-help { background:url(images/ico_mini_help.png); }
.blcg-custom-columns-column-button-warning { background:url(images/ico_mini_warning.png); }
.blcg-custom-columns-tooltip { font-size:11px; }
.blcg-custom-columns-help-text { max-width:300px; margin:0 0 3px; line-height:14px; }
.blcg-custom-columns-help-single-text { margin:0; }
ul.blcg-custom-columns-help-features li { height:16px; padding:0 0 0 16px; line-height:16px; font-size:11px; font-weight:bold; }
.blcg-custom-columns-help-feature-with { background:url(images/check.png) no-repeat left center; }
.blcg-custom-columns-help-feature-without { background:url(images/cross.png) no-repeat left center; }

/* CUSTOM COLUMNS FILTERS */

.grid tr.filter .blcg-categories-filter { position:relative; padding-right:18px; }
.grid tr.filter .blcg-categories-filter .blcg-filter-button { position:absolute; top:0; right:0; }
.grid tr.filter .blcg-filter-value { font-weight:normal; }
.blcg-filter-form-buttons { float:right; }
.adminhtml-blcg-custom-grid-column-filter-categories { background:#fff; }
.adminhtml-blcg-custom-grid-column-filter-categories .middle { background:none; min-height:0; }
.adminhtml-blcg-custom-grid-column-filter-categories .wrapper-popup { min-width:680px; }

/* PINNABLE GRID HEADER */

.blcg-grid-pinned-actions-block { z-index:1; background:#fff; }
.blcg-grid-pinned-actions-block .actions td { padding:0 6px; }
.blcg-grid-pinned-actions-block table.massaction td:first-child { white-space:nowrap; }

/* CATEGORY FILTER TREE ACTION */

.adminhtml-blcg-custom-grid-column-filter-categories { background:#fff; }
.adminhtml-blcg-custom-grid-column-filter-categories .middle { background:none; min-height:0; }
.adminhtml-blcg-custom-grid-column-filter-categories .wrapper-popup { min-width:680px; }
.adminhtml-blcg-custom-grid-column-filter-categories .entry-edit { clear:both; }
.adminhtml-blcg-custom-grid-column-filter-categories .entry-edit .entry-edit-head { background:#666; }

/* WINDOW STYLES */

.dialog.blcg-popup-window { border:0; }
.blcg-popup-window .top.table_window { background:none; border:none; }
.dialog.blcg-popup-window .bot { display:table !important; }

.overlay___invisible__ { background-color:#666; filter:alpha(opacity=0); -moz-opacity: 0; opacity: 0; }
.overlay_blcg { background-color:#000; filter:alpha(opacity=60); -moz-opacity:0.6; opacity:0.6; }
.blcg_wired_frame { display:block; position:absolute; border:1px #000 dashed; background:#fff; filter:alpha(opacity=60); -moz-opacity:0.6; opacity:0.6;	}

.blcg_nw { background:transparent url(window/top-left.png) no-repeat 0 0; width:9px; height:28px; }
.blcg_n { background:transparent url(window/top-middle.png) repeat-x 0 0;	height:28px; }
.blcg_ne { background:transparent url(window/top-right.png) no-repeat 0 0; width:15px; height:28px; }
.blcg_w { background:transparent url(window/left.png) repeat-y top left; width:9px; }
.blcg_e { background:transparent url(window/right.png) repeat-y top right; width:15px; }
.blcg_sw { background:transparent url(window/bottom-left.png) no-repeat 0 0; width:9px; height:15px; }
.blcg_s { background:transparent url(window/bottom-middle.png) repeat-x 0 0; height:15px; }
.blcg_se,
.blcg_sizer { background:transparent url(window/bottom-right-fixed.png) no-repeat 0 0; width:15px; height:15px; }
.blcg-resizable-popup-window .blcg_sizer { background-image:url(window/bottom-right.png); cursor:se-resize; }
.blcg_close { width:15px; height:9px; background:transparent url(window/button-close.png) no-repeat 0 0; position:absolute; top:11px; right:10px; cursor:pointer; z-index:1000; }
.blcg_maximize { width:15px; height:9px; background:transparent url(window/button-maximize.png) no-repeat 0 0; position:absolute; top:11px; right:25px; cursor:pointer; z-index:1000; }
.blcg_minimize { width:15px; height:9px; background:transparent url(window/button-minimize.png) no-repeat 0 0; position:absolute; top:11px; right:40px; cursor:pointer; z-index:1000; }
.blcg_title { float:left; height:24px; line-height:28px; font-size:12px; font-weight:bold; font-family:Verdana, Arial, sans-serif; text-align:left; margin-top:2px; width:100%; color:#fff; }

.blcg_content { overflow:auto; font-size:12px; background:#fff; }
.blcg_content,
.blcg_content label { color:#333; font-family:Arial, sans-serif; }

.blcg-popup-window .blcg_message { padding:0 18px; }
.blcg-popup-window .content-header,
.blcg-column-editor-form .content-header { border-bottom-width:3px; }
.blcg-popup-window .entry-edit .entry-edit-head,
.blcg-column-editor-form .entry-edit .entry-edit-head { background:#666; }
.blcg-popup-window .entry-edit fieldset,
.entry-edit fieldset.blcg-editor-fieldset,
.blcg-popup-window .entry-edit .fieldset,
.entry-edit .fieldset.blcg-editor-fieldset { border-top:0; border-color:#666; }

/* For IE */
* html .blcg_nw { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/top-left.png", sizingMethod="crop"); }
* html .blcg_n { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/top-middle.png", sizingMethod="scale"); }
* html .blcg_ne { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/top-right.png", sizingMethod="crop"); }
* html .blcg_w { background-color:transparent; background-image:none; filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/left.png", sizingMethod="scale"); }
* html .blcg_e { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/right.png", sizingMethod="scale"); }
* html .blcg_sw { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/bottom-left.png", sizingMethod="crop"); }
* html .blcg_s { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/bottom-middle.png", sizingMethod="scale"); }
* html blcg_se,
* html .blcg_sizer { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/bottom-right.png", sizingMethod="crop"); }
* html .blcg_close { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/button-close.png", sizingMethod="crop"); }
* html .blcg_minimize { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="./window/button-minimize.png", sizingMethod="crop"); }
* html .blcg_maximize { background-color:transparent; background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="../window/button-maximize.png", sizingMethod="crop"); }
* html .blcg_content { background:#c7c7c7; }

/* SPECIFIC STYLES */

#sales_order_grid .grid .order-tables tr.headings,
#sales_invoice_grid .grid .order-tables tr.headings,
#sales_shipment_grid .grid .order-tables tr.headings,
#sales_creditmemo_grid .grid .order-tables tr.headings { background:url(../../images/sort_row_bg.gif) repeat-x scroll 0 50% transparent !important; }

#customgrid_rewriters_file.config .comment span { font-family:Courier New,Courier,Arial,Helvetica,sans-serif; }