#amasty_store {
    border: none;
    height: 1700px; /* fallback */
    width: 100%;
}

.amasty-store-tab {
    position: relative;
}

.amasty-store-tab span:after {
    top: 0;
    right: 3px;
    position: absolute;
    content: " ";
    display: block;
    background: url(../../../images/ambase/shop.png) no-repeat;
    width: 55px;
    height: 23px;
}

.ambase-debug-section {
    background: #FFF;
    padding: 10px;
    border: 1px solid #d6d6d6;
}

.ambase-debug-section table td {
    padding: 5px 10px;
    vertical-align: top;
}

.ambase-debug-section .green {
    color: green;
}

.ambase-debug-section .red {
    color: red;
}

.ambase-debug-header {
    font-weight: bold;
    padding: 10px 0;
    font-size: 1.3em;
}

.ambase-show-instruction {
    color: grey;
    text-decoration: underline;
    cursor: pointer;
}

.ambase-instruction {
    font-size: 0.8em;
}

.ambase-instruction li {
    margin: 0;
}

.ambase-file {
    font-size: 0.8em;
    color: grey;
}

.ambase-debug-header-click {
    text-decoration: underline;
    cursor: pointer;
}

.ambase-hidden {
    visibility: hidden;
}

.ambase-notice {
    padding: 10px;
    background-color: #fff9e9;
    display: block;
    margin-bottom: 10px;
    font-size: 15px;
}

.comment .ambase-notice {
    color: #ea7601;
}

/* additional tab in config section*/
.amasty-info-block {
    padding: 5px;
    margin-bottom: 10px;
    font-size: 14px;
}

.amasty-info-block .upgrade-error, .amasty-disable-extensions p, .amasty-conflicts {
    padding: 5px 17px 5px 47px;
    background: #fff9e9 url('../../../images/error_msg_icon.gif') 17px 5px no-repeat;
    border: 1px solid #eee2be;
    margin: 5px 0;
    display: inline-block;
}

.module-version {
    color: #ea7601;
}

.amasty-info-block .module-version.last-version {
    color: green;
}

.amasty-info-block > div {
    margin-bottom: 10px;
}

.amasty-conflicts-title,
.version-title,
.module-version {
    font-size: 16px;
    font-weight: bold;
}

.amasty-conflicts-title {
    margin-top: 20px;

}

.amasty-user-guide,
.amasty-additional-content {
    border: 1px solid #95a486;
    color: #3d6611;
    background: #eff5ea url('../../../images/success_msg_icon.gif') no-repeat 10px 10px;
    min-height: 23px;
    margin-bottom: 11px;
    padding: 8px 8px 2px 32px;
}

.amasty-info-block .amasty-success {
    border: 1px solid #95a486;
    color: #3d6611;
    background: #eff5ea url('../../../images/success_msg_icon.gif') no-repeat 10px 10px;
}

.amasty-info-block .amasty-notice {
    border: 1px solid #ffd967;
    color: #3d6611;
    background: #fffbf0 url('../../../images/note_msg_icon.gif') no-repeat 10px 10px;
}

.amasty-info-block .amasty-warning {
    border: 1px solid #666e73;
    color: #000000;
    background: #e6e6e6 url('../../../images/warning_msg_icon.gif') no-repeat 10px 10px;
}

.amasty-info-block  .amasty-error {
    border: 1px solid #f16048;
    color: #df280a;
    background: #faebe7 url('../../../images/error_msg_icon.gif') no-repeat 10px 10px;
}

.ambase-unsubscribe {
    max-width: 1px;
    position: absolute;
}

.ambase-icon {
    margin-right: 5px;
}

.ambase-module-name {
    line-height: 14px;
    vertical-align: top;
}

#ambase_extensions .form-list tr .value {
    color: #e60303;
}

#ambase_extensions .form-list tr[id$="is_latest"] .value {
    color: #038603;
}

.amasty_not_instaled {
    color: #f70606;
}

.amasty_is_instaled {
    color: #00f702;
}
/*start modified*/

.ambase-updates-block {
    margin-top: 5px;
}

.ambase-updates-block.-last {
    margin-top: 10px;
}

.ambase-updates-block .ambase-title {
    font-weight: bold;
    font-size: 17px;
    color: #303030;
}

.ambase-updates-block .ambase-title.-small {
    padding-top: 15px;
    border-top: 1px solid #ccc;
    font-size: 15px;
}

.ambase-btns-block {
    display: flex;
}

.ambase-btns-block .ambase-btn {
    position: relative;
    padding: 10px 25px;
    border-radius: 4px;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s;
}

.ambase-btns-block .ambase-btn.-orange {
    padding-right: 46px;
    background-color: #eb5202;
}

.ambase-btns-block .ambase-btn.-orange:hover {
    background-color: #ba4000;
}

.ambase-btns-block .ambase-btn.-orange:after {
    position: absolute;
    top: calc(50% - 6px);
    right: 25px;
    width: 10px;
    height: 10px;
    content: '';
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTEiIGhlaWdodD0iMTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjU2NSAwYS40NjQuNDY0IDAgMDAtLjA0Ny4wMDRINy4xOWEuNDYuNDYgMCAwMC0uMzAzLjExLjM4NS4zODUgMCAwMC0uMDk0LjEyNi4zNTQuMzU0IDAgMDAuMDk0LjQyMi40Ni40NiAwIDAwLjMwMy4xMWgyLjM2Mkw0LjM1MyA1LjVhLjM4Ni4zODYgMCAwMC0uMDk1LjEyNS4zNTUuMzU1IDAgMDAuMDkuNDI0Yy4wMzkuMDM2LjA4Ni4wNjUuMTM4LjA4NGEuNDYuNDYgMCAwMC40NjUtLjA5bDUuMi00LjcyNXYyLjE0N2MtLjAwMi4wNTEuMDA5LjEwMi4wMy4xNDkuMDIuMDQ3LjA1Mi4wOS4wOS4xMjZhLjQ2LjQ2IDAgMDAuNjAyIDAgLjM1NC4zNTQgMCAwMC4xMjItLjI3NVYuNDM3YS4zNTEuMzUxIDAgMDAtLjAxMy0uMTY1LjM3OC4zNzggMCAwMC0uMDktLjE0NC40MjUuNDI1IDAgMDAtLjE0OC0uMDk3LjQ2LjQ2IDAgMDAtLjE4LS4wM3oiIGZpbGw9IiNmZmYiLz48cGF0aCBkPSJNNC42NTIgMS41NDJjLTEuMDU1IDAtMS44NTMtLjAwMi0yLjQ4OS4wNjQtLjYzNS4wNjUtMS4xNDQuMjAyLTEuNTA4LjUzNC0uMzY0LjMzLS41MTMuNzkyLS41ODUgMS4zN0MwIDQuMDg3IDAgNC44MTIgMCA1Ljc3YzAgLjk2IDAgMS42ODUuMDcyIDIuMjYzLjA3My41NzcuMjI2IDEuMDQuNTkgMS4zNy4zNjUuMzMuODczLjQ2OCAxLjUwOC41MzNTMy42IDEwIDQuNjUyIDEwYzEuMDUyIDAgMS44NDggMCAyLjQ4Mi0uMDY1LjYzNS0uMDY2IDEuMTQ0LS4yMDQgMS41MDktLjUzNS4zNjQtLjMzMS41MTYtLjc5My41ODktMS4zNy4wNzMtLjU3OC4wNzItMS4zMDIuMDcyLTIuMjYuMDAxLS4wNS0uMDEtLjEtLjAzLS4xNDhhLjM4My4zODMgMCAwMC0uMDkxLS4xMjYuNDYuNDYgMCAwMC0uNjAyIDAgLjM4My4zODMgMCAwMC0uMDkyLjEyNi4zNTQuMzU0IDAgMDAtLjAzLjE0OWMwIC45NTYtLjAwNCAxLjY3LS4wNjcgMi4xNzEtLjA2NC41MDMtLjE4Ljc2My0uMzQ3LjkxNC0uMTY3LjE1Mi0uNDUzLjI1OC0xLjAwNi4zMTUtLjU1Mi4wNTctMS4zMzYuMDYtMi4zODcuMDYtMS4wNSAwLTEuODM1LS4wMDMtMi4zODctLjA2LS41NTItLjA1Ni0uODM4LS4xNjEtMS4wMDUtLjMxMi0uMTY3LS4xNTItLjI4NC0uNDEyLS4zNDctLjkxNEMuODUgNy40NDIuODQ2IDYuNzI5Ljg0NiA1Ljc3Uy44NDggNC4wOTkuOTEgMy41OTZjLjA2Mi0uNTAyLjE3OC0uNzYyLjM0NC0uOTEzLjE2Ni0uMTUuNDUxLS4yNTYgMS4wMDQtLjMxMy41NTQtLjA1NyAxLjM0LS4wNiAyLjM5NC0uMDZhLjQ2LjQ2IDAgMDAuMzAzLS4xMS4zODUuMzg1IDAgMDAuMDkzLS4xMjUuMzU0LjM1NCAwIDAwMC0uMjk3LjM4NS4zODUgMCAwMC0uMDkzLS4xMjYuNDYuNDYgMCAwMC0uMzAzLS4xMXoiIGZpbGw9IiNmZmYiLz48L3N2Zz4=');
}

.ambase-btns-block .ambase-btn.-blue {
    margin-left: 10px;
    padding-right: 37px;
    border: 1px solid #1787e0;
    color: #1787e0;
    background-color: #eaf6ff;
}

.ambase-btns-block .ambase-btn.-blue:hover {
    background-color: #c8e8ff;
}

.ambase-btns-block .ambase-btn.-blue:after {
    position: absolute;
    top: calc(50% - 6px);
    right: 17px;
    width: 10px;
    height: 10px;
    content: '';
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTEiIGhlaWdodD0iMTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjU2NSAwYS40NjQuNDY0IDAgMDAtLjA0Ny4wMDRINy4xOWEuNDYuNDYgMCAwMC0uMzAzLjExLjM4NS4zODUgMCAwMC0uMDk0LjEyNi4zNTQuMzU0IDAgMDAuMDk0LjQyMi40Ni40NiAwIDAwLjMwMy4xMWgyLjM2Mkw0LjM1MyA1LjVhLjM4Ni4zODYgMCAwMC0uMDk1LjEyNS4zNTUuMzU1IDAgMDAuMDkuNDI0Yy4wMzkuMDM2LjA4Ni4wNjUuMTM4LjA4NGEuNDYuNDYgMCAwMC40NjUtLjA5bDUuMi00LjcyNXYyLjE0N2MtLjAwMi4wNTEuMDA5LjEwMi4wMy4xNDkuMDIuMDQ3LjA1Mi4wOS4wOS4xMjZhLjQ2LjQ2IDAgMDAuNjAyIDAgLjM1NC4zNTQgMCAwMC4xMjItLjI3NVYuNDM3YS4zNTEuMzUxIDAgMDAtLjAxMy0uMTY1LjM3OC4zNzggMCAwMC0uMDktLjE0NC40MjUuNDI1IDAgMDAtLjE0OC0uMDk3LjQ2LjQ2IDAgMDAtLjE4LS4wM3oiIGZpbGw9IiMxNzg3RTAiLz48cGF0aCBkPSJNNC42NTIgMS41NDJjLTEuMDU1IDAtMS44NTMtLjAwMi0yLjQ4OS4wNjQtLjYzNS4wNjUtMS4xNDQuMjAyLTEuNTA4LjUzNC0uMzY0LjMzLS41MTMuNzkyLS41ODUgMS4zN0MwIDQuMDg3IDAgNC44MTIgMCA1Ljc3YzAgLjk2IDAgMS42ODUuMDcyIDIuMjYzLjA3My41NzcuMjI2IDEuMDQuNTkgMS4zNy4zNjUuMzMuODczLjQ2OCAxLjUwOC41MzNTMy42IDEwIDQuNjUyIDEwYzEuMDUyIDAgMS44NDggMCAyLjQ4Mi0uMDY1LjYzNS0uMDY2IDEuMTQ0LS4yMDQgMS41MDktLjUzNS4zNjQtLjMzMS41MTYtLjc5My41ODktMS4zNy4wNzMtLjU3OC4wNzItMS4zMDIuMDcyLTIuMjYuMDAxLS4wNS0uMDEtLjEtLjAzLS4xNDhhLjM4My4zODMgMCAwMC0uMDkxLS4xMjYuNDYuNDYgMCAwMC0uNjAyIDAgLjM4My4zODMgMCAwMC0uMDkyLjEyNi4zNTQuMzU0IDAgMDAtLjAzLjE0OWMwIC45NTYtLjAwNCAxLjY3LS4wNjcgMi4xNzEtLjA2NC41MDMtLjE4Ljc2My0uMzQ3LjkxNC0uMTY3LjE1Mi0uNDUzLjI1OC0xLjAwNi4zMTUtLjU1Mi4wNTctMS4zMzYuMDYtMi4zODcuMDYtMS4wNSAwLTEuODM1LS4wMDMtMi4zODctLjA2LS41NTItLjA1Ni0uODM4LS4xNjEtMS4wMDUtLjMxMi0uMTY3LS4xNTItLjI4NC0uNDEyLS4zNDctLjkxNEMuODUgNy40NDIuODQ2IDYuNzI5Ljg0NiA1Ljc3Uy44NDggNC4wOTkuOTEgMy41OTZjLjA2Mi0uNTAyLjE3OC0uNzYyLjM0NC0uOTEzLjE2Ni0uMTUuNDUxLS4yNTYgMS4wMDQtLjMxMy41NTQtLjA1NyAxLjM0LS4wNiAyLjM5NC0uMDZhLjQ2LjQ2IDAgMDAuMzAzLS4xMS4zODUuMzg1IDAgMDAuMDkzLS4xMjUuMzU0LjM1NCAwIDAwMC0uMjk3LjM4NS4zODUgMCAwMC0uMDkzLS4xMjYuNDYuNDYgMCAwMC0uMzAzLS4xMXoiIGZpbGw9IiMxNzg3RTAiLz48L3N2Zz4=');
}

.ambase-modules-block {
    display: flex;
    flex-wrap: wrap;
    margin-top: 12px;
}

.ambase-module-container {
    display: inline-block;
    box-sizing: border-box;
    margin: 0 10px 15px 0;
    padding: 0 10px 10px;
    width: 164px;
    min-height: 128px;
    border: 1px solid #cacaca;
    border-radius: 6px;
    text-align: center;
    box-shadow: 0 16px 20px -15px #dcdcdc;
    background: #f8f8f8;
    text-decoration: none;
}

.ambase-module-container.-update {
    border: 1px solid #eb5202;
    box-shadow: 0 15px 15px -18px #eb5202;
}

.ambase-module-container.-link {
    cursor: pointer;
    transition: border .3s, box-shadow .3s;
    text-decoration: none;
}

.ambase-module-container.-link:hover {
    border: 1px solid #eb5202;
    box-shadow: 0 15px 15px -18px #eb5202;
    text-decoration: none;
}

.ambase-version-block .ambase-warning,
.ambase-version-block .ambase-date {
    margin: 0;
    line-height: 15px;
    font-size: 11px;
    color: #828282;
}
.ambase-version-block .ambase-warning {
    color: red;
}
.ambase-version-block .ambase-link {
    text-decoration: underline;
    font-weight: bold;
    color: #007bdb;
}

.ambase-module-container.-link .ambase-title {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 50px;
    border-bottom: 1px solid #c4c4c4;
    font-size: 14px;
    font-weight: bold;
    color: #303030;
    text-decoration: none;
    transition: border-bottom-color 0.3s ease-in-out;
}

.ambase-module-container.-link:hover > .ambase-title {
    border-bottom: 1px solid #eb5202;
    text-decoration: underline;
}

.ambase-module-container.-update .ambase-version-block {
    border-top: 1px solid #eb5202;
}

.ambase-version-container {
    display: flex;
    justify-content: center;
    margin: 10px 0;
}

.ambase-module-container.-update .ambase-version-container {
    margin-bottom: 6px;
}

.ambase-version-container .ambase-version {
    margin: 0;
    font-weight: bold;
    color: #252525;
}

.ambase-module-container.-update .ambase-version-container .ambase-version {
    color: #eb5202;
}

.ambase-version-container .ambase-svg {
    margin-right: 10px;
    width: 20px;
    height: 20px;
    background: url('data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
}

.ambase-version-container .ambase-svg.-arrow {
    margin: 0 10px;
    width: 40px;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDEiIGhlaWdodD0iOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNDAuMzU0IDQuMzU0YS41LjUgMCAwMDAtLjcwOEwzNy4xNzIuNDY0YS41LjUgMCAxMC0uNzA3LjcwOEwzOS4yOTMgNGwtMi44MjggMi44MjhhLjUuNSAwIDEwLjcwNy43MDhsMy4xODItMy4xODJ6TTAgNC41aDQwdi0xSDB2MXoiIGZpbGw9IiM4MjgyODIiLz48L3N2Zz4=');
    background-repeat: no-repeat;
    background-position: center;
}

.ambase-module-container.-update .ambase-title-container {
    border-bottom: none;
}

.ambase-version-block .line-upper {
    text-decoration: none;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.25px;
    font-weight: bold;
}

.ambase-version-block .line-lower {
    text-decoration: none;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.25px;
    font-style: normal;
}

.ambase-title-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 50px;
    border-bottom: 1px solid #c4c4c4;
}

.ambase-title-container > .ambase-title {
    margin: 0;
    font-size: 14px;
}

.ambase-title-container .ambase-title.-link {
    text-decoration: none;
}

.ambase-title-container .ambase-title.-link:hover {
    text-decoration: underline;
}

.amasty-extensions-tab {
    position: relative;
}

.amasty-extensions-tab:after {
    position: absolute;
    top: 2px;
    right: 3px;
    width: 41px;
    height: 21px;
    border-radius: 4px;
    background: #eb5202;
    color: #fff;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    line-height: 21px;
}

._strong {
    font-weight: bold;
}
