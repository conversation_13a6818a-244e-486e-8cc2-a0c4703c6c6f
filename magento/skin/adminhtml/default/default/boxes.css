/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/***************** WIDGETS ******************
    Loading Indicator
    Tables
    Grid ( ubiquitous, massaction and dynamic)
    Accordion
    Tabs ( vertical and horizontal )
    Messages
    Forms ( setter and elements)
    Switcher
    Space
    Boxes
    Scroller
*/

/**************** STRUCTURE *****************
    Layout
    Header & Footer
    Columns
    Headings
*/

/************** PAGE-SPECIFIC ***************
    Login
    Sales
    Catalog
    Customers
    Newsletter
    System
*/

/********** ALIGNMENT AND CLEARS ***********/


/******************************************************************/
/**************************** WIDGETS *****************************/
/******************************************************************/


/* LOADING INDICATOR
*******************************************************************/
#loading-process {
    position:absolute;
    top:45%;
    left:50%;
    margin-left:-60px;
    border:2px solid #f1af73;
    padding:15px 60px;
    background:#fff4e9;
    color:#d85909;
    font-size:1.1em;
    font-weight:bold;
    text-align:center;
    z-index:501;
    }
#loading-mask {
    background:url(images/blank.gif) repeat;
    position:absolute;
    color:#d85909;
    font-size:1.1em;
    font-weight:bold;
    text-align:center;
    opacity:0.80;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)"; /* IE8 */
    z-index:500;
    }
#loading-mask .loader {
    position:fixed;
    top:45%;
    left:50%;
    width:150px;
    margin-left:-105px;
    padding:15px 30px;
    background:#fff4e9;
    border:2px solid #f1af73;
    color:#d85909;
    font-weight:bold;
    text-align:center;
    z-index:1000;
    }

#message-popup-window-mask { position:absolute; top:0; right:0; bottom:0; left:0; width:100%; height:100%; z-index:980; background-color:#efefef; opacity:.5; -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";/*IE8*/ }
.message-popup { position:absolute; z-index:990; width:407px; top:-9999em; left:50%; margin:0 0 0 -203px; background:#f3bf8f; padding:0 4px 4px; }
.message-popup.show { top:280px; }
.message-popup .message-popup-head { padding:1px 0; }
.message-popup .message-popup-head h2 { padding:0 10px; margin:0; font:bold 12px/19px Arial, Helvetica, sans-serif; color:#644f3b; }
.message-popup .message-popup-head a { float:right; border:1px solid #ea7601; padding:0 12px 0 7px; background:url(images/bkg_btn-close.gif) 0 50% repeat-x !important; font:normal 12px/17px Arial, Helvetica, sans-serif; color:#fff; text-decoration:none !important; cursor:pointer; }
.message-popup .message-popup-head a span { float:left; background:url(images/bkg_btn-close2.gif) 0 50% no-repeat; padding-left:19px; }
.message-popup .message-popup-content { background:#fdf4eb; padding:21px 21px 10px; }
.message-popup .message-popup-content .message-icon { float:left; width:50px; padding:47px 0 0; background-position:50% 0; background-repeat:no-repeat; font-size:10px; line-height:12px; text-transform:uppercase; text-align:center; overflow:hidden; }
.message-popup .message-popup-content .message-critical { background-image:url(images/severity_critical.gif); color:#e41101; }
.message-popup .message-popup-content .message-major { /*background-image:url(images/severity_major.gif);*/ color:#f55600; }
.message-popup .message-popup-content .message-minor { /*background-image:url(images/severity_minor.gif);*/ color:#ff9e22; }
.message-popup .message-popup-content .message-notice { /*background-image:url(images/severity_notice.gif);*/ color:#659601; }
.message-popup .message-popup-content .message-text { float:right; width:295px; min-height:4.5em; overflow:hidden; color:#644f3b; }
.message-popup .message-popup-content .read-more { margin:7px 0 0; text-align:right; }
.message-popup .message-popup-content .read-more a { background:url(images/more_arrow.gif) 0 50% no-repeat; padding-left:14px; text-decoration:underline; }
.message-popup .message-popup-content .read-more a:hover { text-decoration:none; }

/* TABLES
*******************************************************************************/

/* Grid - General */
.grid                           { border-bottom:0; padding-bottom:.5em; }
.grid table                     { width:100%; border:1px solid #cbd3d4; border-bottom:none; }
.grid table.border              { border:1px solid #cbd3d4; }
.grid tbody                     { background:#fff; }    /* Zebra odd-row */
.grid tr.even, .grid tr.even tr { background:#f6f6f6; } /* Zebra even-row */
.grid tr.on-mouse               { background:#fcf5dd; cursor:pointer; } /* Rows mouse-over */
.grid tr.invalid                { background-color:#f5d6c7; }
.grid th, .grid td              { padding:2px 4px 2px 4px; }
.grid th                        { white-space:nowrap; }
.grid td.editable input.input-text { width:50px !important; margin-left:4px !important; }
.grid td input.input-text       { width:86%; }
.grid td input.input-text-export-filter { width:272px; }
.grid td input.input-text-range { width:104px; }
.grid td input.input-text-range-date { width:87px; }
.grid td input.input-inactive   { background:#eee; }
.grid table td                  { border-width:0 1px 1px 0; border-color:#dadfe0; border-style:solid; }
.grid table.border td           { background:#fff !important; }
.grid table td.first,
.grid table td.last             { border-right:0; }
.grid table td.product          { text-align:right; }
.grid table td.empty-text       { padding:15px; }
.grid table td .action-select   { width:100%; }
.grid .separator                { padding:0 4px; color:#b9b9b9; }

.grid tbody.odd tr              { background:#fff !important; }
.grid tbody.even tr             { background:#f6f6f6 !important; }
.grid tbody.odd tr td,
.grid tbody.even tr td          { border-bottom:0; }
.grid tbody.odd tr.border td,
.grid tbody.even tr.border td   { border-bottom:1px solid #dadfe0; }


/* Grid - Pager and Buttons row */
table.actions                   { width:100%; margin:.5em 0; }
table.actions td                { vertical-align:top; }
.pager select                   { width:4em!important; margin:0 4px; }
.pager input.page               { width:2em !important; }
.pager .arrow                   { margin:0 3px; vertical-align:middle; }


/* Grid - Headings */
.grid tr.headings { background:url(images/sort_row_bg.gif) 0 50% repeat-x; }
.grid tr.headings th { border-width:1px; border-color:#f9f9f9 #d1cfcf #f9f9f9 #f9f9f9; border-style:solid; padding-top:1px; padding-bottom:0; font-size:.9em; }
.grid tr.headings th.last { border-right:0; }
.grid tr.headings th.no-link { /* Grid th with no sorting functionality */ padding-top:2px; padding-bottom:1px; color:#67767e; }
.grid tr.headings th span.nobr { display:block; /* FF3 fix */ }
.grid tr.headings th a { display:block; padding:2px 4px 1px 0; color:#2d444f; text-decoration:none; }
.grid tr.headings th a:hover { color:#d85909; text-decoration:none; }
.grid tr.headings th a.sort-arrow-desc,
.grid tr.headings th a.sort-arrow-asc { background:url(images/sort_on_bg.gif) 0 0 no-repeat; border-bottom:1px solid #fff; border-right:1px solid #fff; padding-bottom:2px; }
.grid tr.headings th span.sort-title { display:block; padding:3px 12px 4px 0; line-height:1; }
.grid tr.headings th a.sort-arrow-desc span.sort-title,
.grid tr.headings th a.sort-arrow-asc span.sort-title { background-position:right 50%; background-repeat:no-repeat; padding:3px 12px 2px 8px; }
.grid tr.headings th a.sort-arrow-desc span.sort-title { background-image:url(images/grid_sort_desc.gif); }
.grid tr.headings th a.sort-arrow-asc span.sort-title { background-image:url(images/grid_sort_asc.gif); }


/* Grid - Mass Action */
.massaction { width:100%; height:26px; border:1px solid #d1cfcf; border-bottom:none; background:url(images/massaction_bg.gif) repeat-x 0 100% #ebebeb; font-size:.9em; }
.massaction td                              { width:50%; border-top:1px solid #fff; padding:1px 8px; vertical-align:middle; }
.massaction .entry-edit fieldset .select    { width:auto; /*width:120px;*/ display:inline; }
.massaction .entry-edit fieldset select.validation-failed { border:1px dashed #eb340a !important; background:#faebe7 !important }
.massaction .entry-edit fieldset            { margin:0; padding:0; background:none; border:none; }
.massaction .entry-edit fieldset .field-row { display:inline; }
.massaction .entry-edit .field-row label    { float:none; width:auto; margin-left:13px; }
.massaction .entry-edit                     { margin:0 !important; padding:0; }
.massaction .entry-edit .validation-advice  { display:none !important; }
.massaction a { text-decoration:none; }
.massaction .entry-edit fieldset span.form_row,
.massaction .entry-edit fieldset span.field-row { clear:none !important; display:inline; float:left !important; margin:0; padding:0 5px 0 0; }

.massaction .entry-edit .outer-span { float:left; }

/* Grid - Filter */
.grid tr.filter                  { background:url(images/filter_row_bg.gif) repeat-x #e3eff1; cursor:default; }
.grid tr.filter th               { padding-top:5px; padding-bottom:5px; border:1px solid #bdbdbd; border-width:0 1px 1px 0; white-space:normal; }
.grid tr.filter th.last          { border-right:0; }
.grid tr.filter input.input-text { width:85%; }
.grid tr.filter select           { width:100%; }
.grid tr.filter .range .range-line { margin-bottom:3px; width:100px; }
.grid tr.filter .range div.date  { min-width:121px; }
.grid tr.filter .range input     { float:right; width:50px !important; margin-top:0; }
.grid tr.filter .range select    { float:right; width:56px !important; margin-top:0; }
.grid tr.filter .range .label    { display:block; width:36px; float:left; padding-left:2px; }
.grid tr.filter .date img        { width:15px; height:15px; cursor:pointer; vertical-align:middle; }
.grid .head-massaction select    { width:auto !important; max-width:90px; }
.grid select.select-export-filter,
.grid select.multiselect-export-filter { width:278px; }

/* Grid Footer */
.grid table tfoot tr            { background:#D7E5EF; }
.grid table tfoot tr td         { border-top:1px solid #9babb9; background:#e5ecf2; line-height:1.7em; }
.grid table.border tfoot tr td  { background:#D7E5EF !important; }


/* Dynamic Grid */ /* Used in pages like Catalog -> Attributes */
.dynamic-grid th                { padding:2px;width:100px; }
.dynamic-grid td                { padding:2px; }
.dynamic-grid td input          { width:94px; }
tr.dynamic-grid td,
tr.dynamic-grid th { padding:2px 10px 2px 0; width:auto; }
tr.dynamic-grid input.input-text { width:154px; }
.available { color:#080; font-weight:bold; }
.not-available { color:#800; }


/* ACCORDION
*******************************************************************************/
dl.accordion .grid      { margin-bottom:0; }
dl.accordion dt,
.entry-edit .entry-edit-head { background:#6f8992; padding:2px 10px; }
dl.accordion dt, div.collapseable { margin-top:1px; }
dl.accordion dt a, div.collapseable a { background:url(images/entry_edit_head_arrow_down.gif) 100% 50% no-repeat; color:#fff; display:block; font-weight:bold; text-decoration:none; }
div.disabled { background:#c6cbc9 !important; }
div.disabled a { background-image:url(images/entry_edit_head_arrow_down2.gif) !important; color:#f6f6f6 !important; }

.entry-edit fieldset.collapseable { margin-bottom:10px; }

dl.accordion dt a:hover, div.collapseable a:hover { color:#fff; text-decoration:none; }
dl.accordion dt.open a, div.collapseable a.open  { background:url(images/entry_edit_head_arrow_up.gif) 100% 50% no-repeat; }
dl.accordion dd         { display:none; }
dl.accordion dd.open    { display:block; }
img.accordion-btn       { float:right; margin-top:1px; margin-right:5px; }

/* TABS
*******************************************************************************/

/* Vertical  Tabs */
ul.tabs                         { border-top:1px solid #bebebe; background-color:#e7efef; }
ul.tabs a, ul.tabs span         { display:block; }
ul.tabs a, ul.tabs a:hover      { text-decoration:none; }
/* ul.tabs a.notloaded             { color:#999; } */
ul.tabs a, ul.tabs a:hover      { color:#000; }
ul.tabs, ul.tabs a              { background:url(images/tabs_link_bg.gif) repeat-y 100% #E7EFEF; }
ul.tabs a:hover                 { background-color:#D8E6E6; background-image:url(images/tabs_link_over_bg.gif); }
ul.tabs a.active                { padding:0; border-bottom:1px solid #bebebe; background:none; }
ul.tabs a:hover.active          { padding:0; }
ul.tabs span                    { background:url(images/tabs_span_bg.gif) repeat-x 0 100%; padding:.3em 0.5em .28em 1.5em; cursor:pointer; }
ul.tabs span em                 { float:right; }
ul.tabs a.active span,
ul.tabs a:hover.active span     { background:#fff; font-weight:bold; }
ul.tabs a.subitem               { padding-left:2.2em; }
ul.tabs span.changed,
ul.tabs span.error              { float:right; background:0; padding:0; }
ul.tabs a.changed span.changed  { background:url(images/fam_bullet_disk.gif) 0 0 no-repeat !important; width:16px; height:16px; }
ul.tabs a.error span.error      { background:url(images/fam_bullet_error.gif) 0 0 no-repeat !important; width:16px; height:16px; }
ul.tabs a.changed               { font-style:italic; }

/* Horizontal Tabs */
ul.tabs-horiz                   { margin:0 0 18px 0; background:url(images/horiz_tabs_ul_bg.gif) repeat-x 0 100% #f8f8f8; padding:8px 0 0 5px; }
ul.tabs-horiz li                { float:left; margin:0 4px; }
ul.tabs-horiz li a              { display:block; background:#e2e2e2; border:1px solid #ccc; padding:2px 10px; color:#333 !important; text-decoration:none !important; }
ul.tabs-horiz li a.notloaded    { /* not used for now */ }
ul.tabs-horiz li a.active       { border-bottom:1px solid #fff; background:#fff; }


/* MESSAGES
*******************************************************************************/
.notification-global { padding:5px 27px 5px 47px; background:#fff9e9 url(images/error_msg_icon.gif) 27px 5px no-repeat; border-bottom:1px solid #eee2be; border-top:1px solid #eee2be; font-size:11px; line-height:16px; margin:0 0 -3px; color:#444; position:relative; }
.notification-global-notice { background-image:url(images/note_msg_icon.gif); }
.notification-global .label { color:#eb5e00; }
.notification-global .clickable { cursor:pointer; }
.notification-global span.critical { color:#d20000; }
.notification-global a:hover { text-decoration:none; }

.error,
a.error span,
.required,
.validation-advice { color:#D40707 !important; font-weight:bold !important; }
.notice { color:#ea7601}
.messages ul { border:0 !important; }
.messages li { min-height:23px !important; margin-bottom:11px !important; padding:8px 8px 2px 32px !important; font-size:.95em !important; font-weight:bold !important; }
.messages ul li { margin:0 0 3px 0 !important; border:0 !important; padding:0 !important; }
.error-msg { border:1px solid #f16048 !important; color:#df280a !important; background:#faebe7 url(images/error_msg_icon.gif) no-repeat 10px 10px !important; }
.success-msg { border:1px solid #95a486 !important; color:#3d6611 !important; background:#eff5ea url(images/success_msg_icon.gif) no-repeat 10px 10px !important; }
.notice-msg { border:1px solid #ffd967 !important; background:#fffbf0 url(images/note_msg_icon.gif) no-repeat 10px 10px !important; color:#3d6611 !important; }
.warning-msg { border:1px solid #666e73 !important; background:#e6e6e6 url(images/warning_msg_icon.gif) no-repeat 10px 10px !important; color:#000000 !important; }
.validation-advice { clear:both; min-height:15px; margin:3px 0 0 9px; background:url(images/validation_advice_bg.gif) no-repeat 2px 1px; padding-left:16px; font-size:.95em; font-weight:bold; line-height:1.25em; }
.staging-datetime-advice .validation-advice { background-position:right center; padding:0 16px 0 0; }
input.validation-failed, textarea.validation-failed { background:#fef0ed; border:1px dashed #d6340e; }

/* Noscript Notice */
.noscript { border:1px solid #000; border-width:0 0 1px; background:#ffff90; font-size:12px; line-height:1.25; text-align:center; color:#2f2f2f; }
.noscript .noscript-inner { width:900px; margin:0 auto; padding:12px 0 12px; background:url(images/i_notice.gif) 20px 50% no-repeat; }
.noscript p { margin:0; }

/* For Demo store only */
.demo-notice { margin:0; background:#d75f07; padding:5px 10px 6px 10px; color:#fff; line-height:1em; text-align:center; }


/* FORMS
*******************************************************************************/
select.countries option         { background-repeat:no-repeat; }
.entry-edit .fieldset .tree li,
.entry-edit .tree li            { margin:0; }


/* Entry Edit  */ /* Site-wide form fieldset */
table.form-edit                         { width:100%; }
.box,
.entry-edit fieldset,
.entry-edit .fieldset                   { padding:10px 15px; margin-bottom:15px; }
.entry-edit .entry-edit-head h4         { float:left; padding:0; background:none; margin:0; color:#fff; font-size:1em; line-height:18px; min-height:0; }
.entry-edit .entry-edit-head .tools     { float:right;}
.entry-edit .entry-edit-head strong,
.entry-edit .entry-edit-head a          { color:#fff; font-size:1em; line-height:18px; min-height:0; font-weight:bold}
.entry-edit .content                    { margin-left:0 !important; padding:10px 15px; }
.entry-edit fieldset li,
.entry-edit .fieldset li                { margin:4px 0; }
.entry-edit fieldset span.form_row,
.entry-edit .fieldset span.form_row,
.entry-edit fieldset .field-row .hint,
.entry-edit .fieldset .field-row .hint  { float:left; color:#999; padding-left:12em; }
.entry-edit .form-buttons               { float:right; margin:2px -3px 2px 0pt; }
label.inline                            { float:none !important; width:auto !important; }
.nested-content .entry-edit             { margin-left:2em; }
.nested-content .entry-edit .entry-edit { margin-left:0; }
#coupon_container .entry-edit { min-width:310px; }

/* Form Elements */
input.input-text,textarea,select { border-width:1px; border-style:solid; border-color:#aaa #c8c8c8 #c8c8c8 #aaa; background:#fff; font:12px arial, helvetica, sans-serif; }
select { min-height:17px; /* to set the height for empty selects */ }
input.input-text,textarea       { padding:2px; }
input.qty                       { width:40px !important; }
input.item-qty                  { width:22px !important; }
input.price                     { width:50px !important; text-align:right; }
input[type=text].disabled       { background:#eee; }
select optgroup                 { font-style:normal; }
select optgroup option          { padding-left:10px; }
select optgroup option.even     { background:#f6f6f6; } /* Zebra even-row */
select.multiselect option       { padding:3px 4px; border-bottom:1px solid #ddd; }
.checkboxes li                  { margin:0 0 5px !important; }
.field-100 { background-color:#fff; border-width:1px; border-style:solid; border-color:#aaa #c8c8c8 #c8c8c8 #aaa; padding:2px; }
.field-100 textarea,
.field-100 input.input-text { float:left; width:100% !important; border:0 !important; padding:0 !important; }
@media screen and (-webkit-min-device-pixel-ratio:0) {
    select option:disabled,
    select:disabled option { color:#c9c9c9!important;color:#cacaca!important; text-shadow:2px 2px 2px #000; }
}

/* Form List */ /* Table for default form data */
.form-list                      { width:auto; border:0 !important; }
.form-list td.label,
.form-list td.value,
.form-list td.value2,
.form-list td.note,
.form-list td.scope-label,
.form-list td.use-default       { border:0 !important; padding-top:5px !important; padding-bottom:5px !important; padding-left:5px !important; background:0 !important; }
.form-list td.hidden            { border:0 !important; padding:0 !important; background:0 !important; }
.form-list td.label             { width:200px; }
.form-list td.use-default label { display:inline-block; vertical-align:middle; padding:0 3px; }
.form-list td.label label       { display:block; width:185px; padding-right:15px; padding-top:1px; }
.form-list td.value input.input-text,
.form-list td.value textarea    { width:274px; }
.form-list td.value textarea    { height:15em; }
.form-list td.value select      { width:280px; }
.form-list td.value select.select-date { width:87px; }
.form-list td.note              { background:url(images/note_cell_bg.gif) 6px 10px no-repeat !important; padding-left:18px; }
.form-list td.scope-label       { padding-left:5px; color:#6f8992; font-size:.9em; }
.form-list p.note               { margin:0; padding:0 0 0 13px; background:url(images/note_bg.gif) 1px 6px no-repeat; font-size:11px; }
.form-list td.value p.note      { width:268px; }

.columns .form-list             { width:auto; }
.columns .form-list td.value    { width:300px; padding-right:5px !important; }
.columns .form-list td.value .next-toinput { width:75px; display:inline; margin-right:5px; }
.columns .form-list td.value .next-toselect input.input-text { width:195px!important; display:inline; }

.columns .form-list td.value input[type="checkbox"][id*="use_"] { margin:4px 10px 0 0; vertical-align:top; }
.columns .form-list td.value input[type="checkbox"][id*="use_"] + label { display:inline-block; vertical-align:top; margin:2px 0 0 -7px; }

.fieldset-wide .form-list { width:100% !important; }
.fieldset-wide .form-list td.value { width:auto !important; }
.fieldset-wide .form-list td.value input.input-text,
.fieldset-wide .form-list td.value textarea { width:98% !important; }
/*.fieldset-wide .form-list td.value select { display:block; }*/
.fieldset-wide .form-list td.scope-label { white-space:nowrap; width:1px; }
.fieldset-wide .form-list td.note { width:120px; }

.multi-input                    { margin-bottom:8px; } /* Example: Address fields with 2 input lines */
.grid tr .form-list tr          { background:#fff !important; } /* Follows grid row background-color */
.grid tr.even .form-list tr     { background:#f6f6f6 !important; } /* Follows grid row background-color */
.grid tr.on-mouse .form-list tr { background:#fcf5dd !important; } /* Follows grid row background-color */
.grid tr .form-list             { margin:8px 0; }
.field-row                      { display:block; margin-bottom:5px; }
span.delete-image,
span.delete-file                { display:block; white-space:nowrap; padding-left:25px; }
span.delete-file                { padding:0; }

/* Back compatibility with span forms */
.entry-edit .field-row          { display:block; }
.entry-edit .field-row label    { float:left; width:150px; }

/* Form Button */
.content-buttons.form-buttons,
.content-header .form-buttons   { text-align:right; margin-bottom:0; }
.content-header .content-buttons-placeholder { display:inline !important; }
.content-header .form-buttons   { float:right; }
.content-header td.form-buttons   { float:none; }
.content-header .form-buttons button { margin-bottom:3px; }
.sub-btn-set { border:1px solid #ddd; border-width:0 1px; background:url(images/sub_button_bg.gif) repeat; padding:3px 10px; text-align:right; }
button::-moz-focus-inner { padding:0; border:0; } /* FF Fix */
button { -webkit-border-fit:lines; } /* <- Safari & Google Chrome Fix */
button,
.form-button { border-width:1px; border-style:solid; border-color:#ed6502 #a04300 #a04300 #ed6502; padding:1px 7px 2px 7px; background:#ffac47 url(images/btn_bg.gif) repeat-x 0 100%; color:#fff; font:bold 12px arial, helvetica, sans-serif; cursor:pointer; text-align:center !important; white-space:nowrap; }
button:hover                    { background:#f77c16 url(images/btn_over_bg.gif) repeat-x 0 0; }
button:active                   { background:#f77c16 url(images/btn_on_bg.gif) repeat-x 0 0; }
button span                     { line-height:1.35em; background-repeat:no-repeat; background-position:0 50%; }
button span span                { background:none !important; padding:0 !important; margin:0 !important; display:inline !important; }
button.delete,
button.save,
button.add                      { padding-left:6px; }
button.cancel span,
button.delete span,
button.save span,
button.add span,
button.back span,
button.add-image span,
button.add-widget span { padding-left:20px; }
/* Google Chrome specific fix for empty buttons */
button.add span:after,
button.delete span:after { display:inline-block; clear:both; content:"."; font-size:0; line-height:0; height:0; overflow:hidden; }
button.back { border-color:#ccc #aaa #aaa #ccc; background-color:#fff; background-image:url(images/btn_back_bg.gif); color:#555; }
button.back span { background-image:url(images/icon_btn_back.gif); }
button.fail,
button.cancel,
button.delete { border-color:#d24403 #a92000 #a92000 #d24403; background-image:url(images/cancel_btn_bg.gif); background-color:#fcaf81; color:#fff; }
button.fail:hover,
button.cancel:hover,
button.delete:hover { background-image:url(images/cancel_btn_over_bg.gif); }
button.fail:active,
button.cancel:active,
button.delete:active { background-image:url(images/cancel_btn_active_bg.gif); background-color:#e0612f; }
button.cancel span,
button.delete span { background-image:url(images/cancel_btn_icon.gif); }
button.add span     { background-image:url(images/add_btn_icon.gif); }
button.save span    { background-image:url(images/save_btn_icon.gif); }
button.show-hide span { background-image:url(images/btn_show-hide_icon.gif); padding-left:26px; }
button.add-image span { background-image:url(images/btn_add-image_icon.gif); }
button.add-widget span { background-image:url(images/btn_add-widget_icon2.gif); padding-left:24px; }
button.add-variable span { background-image:url(images/btn_add-variable_icon.gif); padding-left:26px; }
button.go span { background-image:url(images/btn_go.gif); padding-left:16px; }
button.btn-chooser { display:block; margin:0 0 10px; }
button.success { background-image:url(images/btn_gr_bg.gif); border-color:#46745E; }
button.success:hover { background:url(images/btn_gr_over.gif) #6cac46; }
button.success:active { background:url(images/btn_gr_on.gif) repeat-x 0 0 #3fa05e; }

button.disabled,
button.disabled:hover,
button.disabled:active { border-color:#777 #505050 #505050 #777; background:#919191 url(images/btn_bg-disabled.gif) 0 0 repeat-x; color:#fff; cursor:default; opacity:.8; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=20)"; }
button.add.disabled span { background-image:url(images/add_btn_icon-disabled.gif); }

button.loading > span { padding-left:20px; background-image: url(images/btn_loading-icon.gif) !important;}

button.icon-btn { width:32px !important; }
button.icon-btn span { text-indent:-999em; display:block; width:16px; padding:0; overflow:hidden; }

.buttons-set { margin:0 0 10px; }
.buttons-set button { margin-right:5px; }


/* SWITCHER
*******************************************************************************/
.switcher { margin-bottom:10px; border:1px solid #cddddd; background:#e7efef; padding:10px; }
.side-col .switcher { padding-right:26px; }
.side-col .switcher label { display:block; }
.side-col .switcher .link-store-scope { float:right; margin-right:-19px; margin-left:3px; }
.side-col .switcher select { width:100%; float:left; }
/*.side-col .switcher     { margin-right:20px; margin-bottom:20px; }*/
.catalog-categories .side-col .switcher { margin-right:0; margin-bottom:15px; }
.link-store-scope { display:inline-block; vertical-align:middle; margin:0 0 1px; width:16px; height:16px; background:url(images/i_question-mark.png) 0 0 no-repeat; text-decoration:none !important; text-indent:-999em; overflow:hidden; }
.store-scope .link-store-scope { float:left; margin-right:10px; }
.store-scope .tree-store-scope { float:left; padding:7px 10px; border:1px dotted #dedede; }
.store-scope table.stores-tree { float:left; width:auto !important; }
.form-list td.value .store-scope { white-space:nowrap; }
.form-list td.value .link-store-scope { float:none; margin-right:0; vertical-align:top; margin-top:6px; }


/* SPACE
*******************************************************************************/
.box-left, .box-right   { width:48.5%; }
.box-left               { float:left; }
.box-right              { float:right; }
.box-left .content,
.box-right .content     { padding:6px 14px; }
.separator, .pipe       { padding:0 6px; font-size:.9em; } /* vertical pipe */
.divider                { display:block; height:1px; margin:8px 0; background:#ddd; overflow:hidden; }  /* horizontal pipe */
td.divider              { font-size:1px; line-height:0; }
.box,
.entry-edit fieldset,
.entry-edit .fieldset { border:1px solid #d6d6d6; background:#fafafa; }


/* SCOLLER */ /* Used primarily in Sales -> Order -> Create Order */ /*
*******************************************************************************/
.scroll-cont {
    position:absolute;
    top:-25px;
    left:503px;
    width:16px;
    height:265px;
    background:transparent url(images/db-scroll-bg.gif) no-repeat top;
    z-index:10000;
    }
.auto-scroll                { overflow:auto; height:11em; }
.root                       { position:relative; height:260px; margin:0; width:1px; }
.thumb                      { position:absolute; height:40px; width:16px; margin-top:-28px; z-index:11000; }
.up                         { margin-top:254px; height:16px; }
.dn                         { margin-top:0; padding:0; height:15px; }
.up a, .up a img,
.dn a, .dn a img,
.thumb a ,
.thumb a img                { border:0; }
.up a:active, .dn a:active  { outline:none; }
.scrollContainer {
    position:absolute;
    left:0;
    top:19px;
    clip:rect(0 467px 200px 0);
    overflow:auto;
    border:0;
    }
.scrollContent              { position:absolute; left:0; top:0; }

/* Horizontal scroll */
.hor-scroll                 { width:100%; overflow:auto; padding-bottom:4px; margin-bottom:-4px; } /* IE has personal style, see iestyles.css */


/**/
.note-list { width:100%; overflow:hidden; }
.note-list li { border-top:1px solid #e3e3e3; margin-top:9px !important; background:url(images/icon_note_list.gif) no-repeat 0 4px; padding-bottom:9px; padding-left:18px; }



/******************************************************************************/
/********************************** STRUCTURE *********************************/
/******************************************************************************/

/* LAYOUT
*******************************************************************************/
.wrapper { min-width:960px; }
.wrapper-popup { min-width:750px; }
.header { background:url(images/header_top_bg.gif) repeat-x #425e66; text-align:right; }
.middle { min-height:450px; background:url(images/simple_container_bg.gif) repeat-x #fff; padding:23px 27px 0 27px; }
.middle-popup { border-bottom:3px solid #fff; background:url(images/middle_bg.gif) repeat-x 0 100% #fff; padding:0 0 0 0; background:yellow; }
.container-collapsed { padding:1.8em 2.2em 1.8em 2em; padding-top:0; }
.columns {background:url(images/side_col_bg.gif) repeat-y 217px 0; }

div.side-col { float:left; width:220px; margin-right:-220px; padding-bottom:25px; }
div.main-col { margin-left:220px; min-height:450px; padding:0 0 25px 25px; }
div.main-col-inner { float:left; /* Fixes some inner clears in the liquid main-col */ width:100%; }

.footer { clear:both; background:url(images/footer_bg.gif) repeat-x #e6e6e6; padding:105px 2.8em 2.8em 2.8em; font-size:.95em; text-align:center; }
.simple-container-popup { min-height:50px !important; padding:1.8em 2.3em 6em 2.3em; background:url(images/simple_container_bg.gif) repeat-x; }

.page-popup { background:#fff; }


/******************************************************************************/
/************************************* BOXES **********************************/
/******************************************************************************/

/* HEADER & FOOTER
*******************************************************************************/

.logo                                   { float:left; margin:5px 20px 5px 27px; height:43px; }
.header-top                             { border-bottom:1px solid #5F767F; }

/* Header right */
.header-right                           { padding:10px 25px 0 15px; font-size:.95em; color:#fff; }
.header-right a, .header-right a:hover  { color:#fcce77; }
.header-right .separator                { color:#999; }
.header-right fieldset                  { display:inline; padding-left:10px; }
.header-right fieldset input.input-text { width:18em; }
.header-right .super {
    float:right;
    line-height:1.8em;
    margin-bottom:14px;
    margin-left:1.3em;
    }

/* Search autocomplete */
div.autocomplete {
    z-index:10000;
    position:absolute;
    width:250px;
    background-color:white;
    border:1px solid #888;
    margin:0;
    padding:0;
    }
div.autocomplete ul { margin:0; padding:0; }
div.autocomplete ul li.selected { background-color:#dcebf0; }
div.autocomplete ul li { padding:.5em .7em; min-height:32px; cursor:pointer; text-align:left; color:#2f2f2f; line-height:1.3em; }

/* Footer */
.footer .bug-report                     { float:left; width:35%; text-align:left; }
.footer .legality                       { float:right; width:35%; min-height:19px; padding-left:22px; text-align:right; }

/* COLUMNS
********************************************************************************************/

.catalog-categories .side-col   { width:25em; padding-right:25px; }  /* Catalog/Categories */
.catalog-categories .main-col   { padding-left:25px; margin-left:25em; }              /* Catalog/Categories */
.order-summary .side-col        { padding-right:25px; }             /* Order/Create */
.order-summary .main-col        { padding-left:25px; }              /* Order/Create */


/* HEADINGS
********************************************************************************************/

/* Content Header */
.content-header {
    margin-bottom:18px;
    border-bottom:4px solid #dfdfdf;
    padding-bottom:.25em;
    }
.content-header table           { width:100%; }
.content-header h3              { float:left; margin:.3em .5em 0 0; color:#eb5e00; font-size:1.25em; line-height:1.2em; }
.content-header .head h3        { float:none; }
.content-header .button-set     { white-space:nowrap; text-align:right; }
.content-header .content-buttons  { white-space:nowrap!important; margin:0; }
.content-header td.content-buttons  { width:13%;white-space:nowrap!important; margin:0; }
.content-buttons button,
.content-header button,
.filter-actions button          { margin:0 0 0 5px; }
.side-col .content-header       { border-bottom:0; margin-right:12px; margin-bottom:.6em; }
.catalog-categories .side-col .content-header { margin-right:0; }  /* Catalog/Categories */

.left-col-block { width:200px; }

/* Floating Content Header */ /* Used to make action buttons always within reach */
.content-header-floating { display:none; position:fixed; left:0; top:0; width:100%; border-bottom:solid 1px #988753; z-index:100; background:#fdfaa4; -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)"/* IE8 */; opacity:.85; }
.content-header-floating .content-header { padding:6px 8px 4px 8px; margin-bottom:0; border:0; }
.content-header-floating td     { padding-right:20px; }
.content-header-floating button { margin-top:3px; }
.content-header-floating .content-buttons { float:right; /* margin:12px 25px 0 0;*/ }
.content-buttons { margin-bottom:5px; float:right; display:inline; white-space:nowrap; }
.content-header-floating .form-buttons {padding-right:20px; }
.content-header-floating h3 {margin-left:20px; display:inline; }



/* Box Head */
.box-head       { margin-bottom:.6em; text-align:right; }
.box-head h4    { float:left; margin-bottom:0; }


/* Icon Head */ /* Headings with icon preceding text*/
.icon-head                      { min-height:18px; background-repeat:no-repeat; background-position:0 0; padding-left:22px; }
.head-customer-address-list     { background-image:url(images/fam_house.gif); }
.head-edit-form                 { background-image:url(images/fam_page_white.gif); }
.head-customer-view             { background-image:url(images/fam_status_online.gif); padding-left:18px; }
.head-customer,
.head-customer-groups           { background-image:url(images/fam_group.gif); }
.head-user                      { background-image:url(images/fam_user.gif); }
.head-user-edit                 { background-image:url(images/fam_user_edit.gif); }
.head-user-comment              { background-image:url(images/fam_user_comment.gif); }
.head-comment                   { background-image:url(images/fam_comment.gif); }
.head-cart                      { background-image:url(images/fam_cart.gif); }
.head-account                   { background-image:url(images/fam_account.gif); }
.head-online-visitors           { background-image:url(images/fam_monitor.gif); }
.head-products                  { background-image:url(images/fam_package.gif); }
.head-catalog-product           { background-image:url(images/fam_package.gif); }
.head-newsletter-queue          { background-image:url(images/fam_newspaper_go.gif); }
.head-newsletter-list           { background-image:url(images/fam_newspaper.gif); }
.head-newsletter-report         { background-image:url(images/fam_newspaper_error.gif); }
.head-tag, .head-tag-product    { background-image:url(images/fam_tag_orange.gif); }
.head-sales-order,
.head-sales-invoice             { background-image:url(images/fam_folder_table.gif); }
.head-categories                { background-image:url(images/fam_folder_database.gif); padding-left:20px; color:#253033 !important; }
.head-catalog-product-attribute { background-image:url(images/fam_rainbow.gif); padding-left:24px; }
.head-product-attribute-sets    { background-image:url(images/fam_folder_palette.gif); padding-left:23px; }
.head-tax                       { background-image:url(images/fam_money_add.gif); }
.head-cms-page, .head-cms-block { background-image:url(images/application_view_tile.gif); }
.head-backups-control           { background-image:url(images/fam_server_database.gif); }
.head-money, .head-promo-quote  { background-image:url(images/fam_money.gif); }
.head-shipping-address,
.head-billing-address           { background-image:url(images/fam_house.gif); }
.head-shipping-method           { background-image:url(images/fam_lorry.gif); }
.head-payment-method            { background-image:url(images/fam_creditcards.gif); }
.head-order-date                { background-image:url(images/fam_calendar.gif); }
.head-customer-sales-statistics { background-image:url(images/fam_money.gif); }
.head-notification              { background-image:url(images/fam_folder_table.gif); }
.head-compilation               { background-image:url(images/fam_package_go.gif); }



/******************************************************************************/
/********************************* PAGE SPECIFIC ******************************/
/******************************************************************************/


/* LOGIN
*******************************************************************************/
#page-login                             { background:#f8f8f8; text-align:center; }
.login-container                        { width:581px; margin:170px auto; padding-left:32px; background:url(images/login_logo.gif) no-repeat; }
.login-form                             { padding:27px 57px 35px 57px; background:url(images/login_box_bg.jpg) no-repeat; text-align:left; }
.login-form .input-left                 { float:left; }
.login-form .input-right                { float:right; }
.login-form .input-box input.input-text { width:224px; }
.login-form h2                          { font-size:1.7em; font-weight:normal; }
.login-form label                       { font-weight:bold; }
.login-form .form-buttons               { margin:12px 0 0 0; clear:both; text-align:right; }
.login-box .bottom                      { width:581px; height:5px; background:url(images/login_box_bottom.jpg) no-repeat; overflow:hidden; }
.login-container .legal                 { margin:0; background:url(images/login_box_legal_bg.gif) no-repeat; padding:8px 8px 5px 8px; font-size:.95em; }
.login-form .validation-advice          { margin:3px 0 0 0; }
.login-form .forgot-link                { margin:0 17px 0 0; text-align:right; }
.login-form .captcha                    { padding:10px 0 0; }
.login-form .captcha-image              { border:1px solid #aaa; margin:1.5em 0 0; position:relative; }
.login-form .captcha-image img          { vertical-align:bottom; }
.login-form .captcha-reload             { cursor:pointer; position: absolute; top:2px; right:2px; }
.login-form .captcha-reload.refreshing  { animation:rotate 1.5s infinite linear; -webkit-animation:rotate 1.5s infinite linear; -moz-animation:rotate 1.5s infinite linear; }

@-webkit-keyframes rotate {
    0% { -webkit-transform:rotate(0); }
    0% { -webkit-transform:rotate(360deg); }
}
@-moz-keyframes rotate {
    0% { -moz-transform:rotate(0); }
    0% { -moz-transform:rotate(360deg); }
}
@keyframes rotate {
    0% { transform:rotate(0); }
    0% { transform:rotate(360deg); }
}


/* DASHBOARD
*******************************************************************************/
.dashboard-container                    { border:1px solid #ccc; }
.dashboard-container .switcher          { margin-bottom:0;  border:0; }

/* STORE MANAGEMENT
*******************************************************************************/
.adminhtml-system-store-deletestore .content-footer .content-buttons,
.adminhtml-system-store-deletegroup .content-footer .content-buttons,
.adminhtml-system-store-deletewebsite .content-footer .content-buttons { float:left; }


/* SALES
*******************************************************************************/

/* Disabled block */
.overlay span { display:block; width:100%; text-align:center; position:absolute; top:50%; margin:-5px 0 0; color:#000; }

/* Prices */
.price { white-space:nowrap !important; }

/* Incl tax (for order tables) */
.price-incl-tax { display:block; }
.price-incl-tax .label { display:block; white-space:nowrap; }
.price-incl-tax .price { /*display:block;*/ font-weight:bold; }

/* Excl tax (for order tables) */
.price-excl-tax  { display:block; }
.price-excl-tax .label { display:block; white-space:nowrap; }
.price-excl-tax .price { /*display:block;*/ font-weight:bold; }

/* Orders / Invoices / Shipments / Credit Memos Table */
.order-tables td h5.title { font-size:1em; font-weight:bold; }
.order-tables td .option-label { font-weight:bold; font-style:italic; }
.order-tables td .option-value { padding-left:10px; }
.order-tables td .qty-table { border:0 !important; width:100% }
.order-tables td .qty-table td { border:0 !important; padding:0 4px !important; }

dl.item-options dt { font-weight:bold; font-style:italic; }
dl.item-options dd { padding-left:10px; }

ul.item-options li { padding-left:.7em; }


/* Create Order */
.page-create-order .side-col            { width:260px; background:none; padding:0; }
.page-create-order .main-col            { padding-left:28px; }
.page-create-order .switcher            { margin-bottom:25px; }
.create-order-sidebar-container         { border:1px solid #d6d6d6; background:#fafafa; }
.create-order-sidebar-block .content    { margin:0 !important; padding:4px 6px; }
.create-order-sidebar-block .head       { border-top:1px solid #ddd; background:#ececec; padding:2px 6px 1px; font-size:.9em; text-align:right; }
.create-order-sidebar-block .head h5    { float:left; margin:0; color:#2c464f; text-align:left; }
.create-order-sidebar-block .content table td,
.create-order-sidebar-block table th    { padding:0 3px 0 0; }
.create-order-sidebar-block select      { width:170px; }
.create-order-sidebar-block ul          { margin-left:20px; }
.order-choose-address                   { margin:0 0 10px 0;padding:8px 15px;background:#e7efef; } /*Address Selector */
.order-save-in-address-book             { margin:0 0 0 0;padding:8px 15px;background:#e7efef; } /* Save in Address Book Checkbox */
.entry-edit .content .form-list         { width:100%; }

.entry-edit .order-address td.label label { width:100px; }
.entry-edit .order-address input.input-text,
.entry-edit .order-address .textarea     { width:95% !important; }
.entry-edit .order-address .select       { width:96.5%; }
.entry-edit .order-address .validate-vat { text-align:right; padding:10px 0 0; width:96%; }
.order-search-items .entry-edit .grid    { height:610px; overflow:auto; }
.order-search-items .entry-edit .grid table { width:99.9%; }
/* .create-order-totals                 { background:url(images/bg_create_order_totals.gif) repeat-y 50% 0 !important; } */

/* Product Configuration Popup */
#popup-window-mask,
.popup-window-mask { background:url(images/bg_window_mask.png) repeat 0 0; background:rgba(239, 239, 239, 0.5); position:absolute; top:0; right:0; bottom:0; left:0; width:100%; height:100%; z-index:399; }
.product-configure-popup { background:#fafafa; border:1px solid #d6d6d6; left:50%; margin:0 0 0 -271px; position:fixed; top:50%; width:540px; z-index:400; }
.product-configure-popup .validation-advice { margin-left:0; }
#product_composite_configure_messages { margin-left:0 !important; padding:10px 15px; }
.product-configure-popup .content { max-height:400px; overflow:auto; }
.product-configure-popup .content .grid { padding:0; }
.product-configure-popup .content .grid table { border-bottom:1px solid #CBD3D4; }
.product-configure-popup .product-options { border-bottom:1px solid #e7e7e7; margin:0 0 15px; padding:0 0 12px; }
.product-configure-popup .product-options .required { color:#333 !important; font-weight:normal !important; }
.product-configure-popup .product-options .required em { color:#d40707; }
.product-configure-popup .last-fieldset .product-options { border:0 none; margin-bottom:0; padding-bottom:0; }
.product-configure-popup .buttons-set { margin:0; padding:10px; }
.product-configure-popup .buttons-set button { margin:0 0 0 5px; }

/* Gift Card Product */
.giftcard-form .availability { font-weight:bold; margin-bottom:1em; }
.giftcard-form .in-stock { color:#1b6800; }
.giftcard-form .out-of-stock { color:#cf3a00; }
.giftcard-form .price-box { margin:1em 0; }
.giftcard-form label { float:left; width:25%; }

.giftcard-amount-form { margin:0 0 10px; }
.giftcard-amount-form li { overflow:hidden; width:100%; }
.giftcard-amount-form .field { overflow:hidden; margin-bottom:10px; width:100%; }
.giftcard-amount-form .form-list .input-text { width:70px; }
.giftcard-amount-form .form-list select { width:150px; }
.giftcard-amount-form .form-list .input-box { float:left; width:75%; }
.giftcard-amount-form .form-list .input-box .v-fix { white-space:nowrap; }
.giftcard-amount-form .form-list .input-box p.notice { margin:5px 0 0; font-size:11px; white-space:nowrap; }
.giftcard-amount-form .form-list .input-box p.notice span { display:block; margin-right:5px; }

.giftcard-send-form .field { overflow:hidden; margin-bottom:10px; width:100%; }
.giftcard-send-form .field .input-text { width:98%; }
.giftcard-send-form .form-list .input-box { float:left; width:75%; }
.giftcard-send-form .form-list textarea { height:100px; width:98%; }

/* Product Options */
.product-options { margin:10px 0 0; }
.product-options dt label { font-weight:bold; font-size:12px; }
.product-options dt .qty-holder { float:right; }
.product-options dt .qty-holder label { vertical-align:middle; }
.product-options dt .qty-disabled { background:none; border:0; padding:3px; color:#000; }
.product-options dd { margin:5px 0 15px; padding:0 0 12px; border-bottom:1px solid #e7e7e7; }
.product-options dd.last { border-bottom:0; margin-bottom:0; padding-bottom:0; }
.product-options dd .input-box { display:block; }
.product-options dd input.input-text { width:98%; }
.product-options dd input.datetime-picker { width:150px; }
.product-options dd .time-picker { display:-moz-inline-box; display:inline-block; padding:2px 0; vertical-align:middle; }
.product-options dd .time-picker select { vertical-align:middle; }
.product-options dd textarea { width:98%; height:8em; }
.product-options dd select { width:100%; }
.product-options .options-list {}
.product-options .options-list li { vertical-align:middle; margin:0; padding:2px 0; line-height:1.315; }
.product-options .options-list input.radio { float:left; margin:2px -18px 0 0; }
.product-options .options-list input.checkbox { float:left; margin:2px -20px 0 0; }
.product-options .options-list .label { display:block; margin-left:20px; }
.product-options li { margin:3px 0; }

/* Wishlist Bundle Product Options */
.bundle-product-options { padding:5px 0 0 13px; }
.bundle-product-options dl { padding:5px 0 0 15px; }
.bundle-product-options dt { color:#111; }
.bundle-product-options dd { color:#5b5b5b; margin-bottom:10px; }

/* Order Totals */
.order-totals                           { margin-left:auto; border:1px solid #d7c699 !important; padding:12px 0; background:#fcfac9; text-align:right; }
.order-totals table                     { border:none; background:none; margin-left:auto; }
.order-totals table td                  { padding:3px 20px 3px 10px; }
.order-totals table td.label            { white-space:normal; padding:3px 10px 3px 20px; }
.order-totals table td.last             { padding:2px 6px; }
.order-totals-bottom                    { padding:0 20px; }
.order-totals table .validation-advice  { text-align:right; white-space:nowrap; background-position:100% 1px; padding-right:14px; padding-left:0; }
.grand-total, .grand_total              { font-size:1.2em; font-weight:bold; color:#eb4d00 !important; }

/* Shopping cart total summary row expandable to details */
.summary-total { cursor:pointer; }
.summary-total td { padding-top:5px !important; padding-bottom:5px !important; }
.summary-total .summary-collapse { float:right; padding-left:20px; background:url(images/bg_collapse.gif) 0 4px no-repeat; text-align:right; cursor:pointer; }
.show-details .summary-collapse { background-position:0 -52px; }
.show-details td { border-top:1px solid #eae1b2; }
.summary-details td { font-size:11px; background-color:#fdfcdf; }
.summary-details-first td { border-top:1px solid #eae1b2; }

/* Order Constants */
.payment-methods dt                     { margin-bottom:3px; }
.payment-methods dd                     { margin-left:20px; }
.payment-methods .validation-advice     { margin-left:2px; }

/* Cards List */
.cards-list .offset { margin-left:10px; }
.cards-list .info-table td { padding:2px 7px 2px 0; text-align:left; vertical-align:top; }

.release-amounts { margin:0.5em 0; }

/* Centinel */
.centinel .authentication { background:#fff; }
.centinel .authentication iframe { width:99%; height:400px; background:transparent !important; margin:0 0 7px !important; padding:0 !important; border:1px solid #ddd !important; }

/* Gift Messages */
.giftmessage-order-create .entire-order,
.giftmessage-order-create .each-order-item { background-color:#eee; padding:6px 14px; height:275px;  }
.giftmessage-order-create .entire-order .entry-edit,
.giftmessage-order-create .each-order-item .scroll { height:250px; overflow:auto; }
.giftmessage-order-create .single { width:100%; float:left; padding:1.2em 1.5em; }
.giftmessage-order-create h5 { font-size:12px; font-weight:normal; line-height:1.5; margin-bottom:10px; }
.giftmessage-order-create h6 { font-size:11px; font-weight:normal; line-height:1.55; background-color:#cfcfcf; margin-bottom:10px; padding:2px 6px; width:95%; }
.giftmessage-order-create .fieldset { padding:0; }
.giftmessage-order-create .form-list { width:100%; }
.giftmessage-order-create .form-list td.label label { width:100px; }
.giftmessage-order-create .form-list td.value input.input-text,
.giftmessage-order-create .form-list td.value textarea { width:95%; }

.giftmessage-single-item                            { padding:0 !important; }
.giftmessage-single-item .item-container            { cursor:auto; }
.giftmessage-single-item .item-text                 { padding:2px 4px; }
.giftmessage-single-item .gift-form                 { margin-top:3px; background:#f7f6f4 url(images/gift-message-grid-column-bg.gif) 0 0 repeat-x; }
.giftmessage-single-item .gift-form .entry-edit fieldset { border:none !important; margin-bottom:0; background:none !important; padding:15px; }
.giftmessage-single-item .gift-form .entry-edit fieldset .last { margin-bottom:0; }
.giftmessage-single-item .gift-form .entry-edit input.input-text { width:75% !important; }
.giftmessage-single-item .gift-form .entry-edit textarea { width:96% !important; }
.giftmessage-single-item .action-link-holder        {}
.giftmessage-single-item .action-link { padding-right:10px; background:url(images/gift-message-expand.gif) 100% 50% no-repeat; cursor:pointer; }
.giftmessage-single-item .open { background:url(images/gift-message-collapse.gif) 100% 50% no-repeat; }

.giftmessage-whole-order-container .entry-edit input.input-text { width:280px !important; }
.giftmessage-whole-order-container .entry-edit textarea { width:99% !important; padding:2px 3px; }
.giftmessage-whole-order-container .entry-edit label { width:121px; }

/* PayPal */

.pp-general-uk > .config-heading .heading strong,
.pp-method-payflow > .config-heading .heading strong,
.pp-method-express > .config-heading .heading strong,
.pp-method-general > .config-heading .heading strong { padding-left:56px; background:url(images/paypal/logo-paypal.png) no-repeat 0 2px; }
.pp-method-general > .config-heading .button-container { padding:8px 0 0 180px; background:url(images/paypal/pp-allinone.png) no-repeat 0 0; height:26px; }
.pp-method-express > .config-heading .button-container { padding:0 0 0 174px; background:url(images/paypal/pp-alt.png) no-repeat 0 0; height:29px; }
.pp-method-payflow > .config-heading .button-container { padding:0px 0 0 99px; background:url(images/paypal/pp-gateways.png) no-repeat 1px 0; height:36px; }
.pp-general-uk > .config-heading .button-container { padding:9px 0 0 201px; background:url(images/paypal/pp-uk.png) no-repeat 0 0; height:27px; }
.payflow-settings-notice {padding-bottom: 1em;max-width: 660px;}
.payflow-settings-notice .important-label {color:red;}
.payflow-settings-notice ul.options-list {list-style:disc;padding:0 2em;}


/* Packaging for Shipping Popup */
#popup-window-mask,
.popup-window-mask { background:url(images/bg_window_mask.png) repeat 0 0; background:rgba(239, 239, 239, 0.5); position:absolute; top:0; right:0; bottom:0; left:0; width:100%; height:100%; z-index:399; }
.packaging-window,
.packed-window { background:#fafafa; border:1px solid #d6d6d6; left:50%; margin:-200px 0 0 -471px; position:fixed; top:50%; width:1100px; z-index:400; -webkit-box-shadow: 0px 3px 5px #ccc; -moz-box-shadow:0px 3px 5px #ccc; box-shadow:0px 3px 5px #ccc; z-index:400; }
.packaging-window .entry-edit-head { padding:3px 5px; }
.packaging-window .entry-edit-head button { float:right; }
.packaging-window .messages { border:1px solid #f16048; color:#df280a; background:#faebe7 url(images/error_msg_icon.gif) no-repeat 10px 10px; font-weight:bold; margin-bottom:10px; padding:10px 10px 10px 35px; }
.packaging-window .validation-failed { background:#fef0eD; border:1px dashed #D6340E; }
.packaging-window .packaging-content { overflow:auto; height:400px; height:auto !important; max-height:400px; margin:0 0 10px; padding:10px 10px 0; }
.packaging-window .package-block { background:#f6f6f6; border:2px solid #d4d4d4; margin:0 0 10px; padding:10px; }
.packaging-window .package-options { width:100%; border-top:1px solid #ccc; padding:10px 0 0; margin:3px 0 0; }
.packaging-window .package-options td { vertical-align:middle; }
.packaging-window .package-options select { width:130px; }
.packaging-window .package-options .input-text { width:50px; }
.packaging-window .package_prapare { margin-bottom:15px; }
.packaging-window .package-options .customs-value { width:80px; }
.packaging-window .package-options .options-weight { width:75px; }
.packaging-window .package-options .options-units-weight { width:45px; }
.packaging-window .package-options .options-units-dimensions { width:45px; }
.packaging-window .package-options .options-content-type { width:120px; }
.packaging-window .package-options input[type=text].disabled { background:#eee; }
.packaging-window .package-options select.disabled { background:#eee; }
.packaging-window .package-options-contents { border-top:0; }
.packaging-window .package-add-products { border-top:1px solid #ccc; padding:10px 0 0; margin:10px 0 0; }
.packaging-window .package-add-products .grid { padding:0; }
.packaging-window .package-add-products .grid button { vertical-align:middle; }
.packaging-window .package-number {font-weight: bold;}
.packaging-window .package-number span {margin-left: 5px;}

.packed-window .entry-edit-head { padding:3px 5px; }
.packed-window .packed-content { padding:10px 10px 0; overflow:auto; max-height:400px; }
.packed-window .package { background:#fefefe; border:7px solid #d5d5d5; margin-bottom:10px; padding:10px; }
.packed-window .package h4 { background:#fefefe; border:solid #ccc; border-width:0 0 1px 1px; float:right; color:#222; font-size:12px; margin:-10px -10px 0 0; padding:5px 10px; position:relative; z-index:100; }
.packed-window .package strong{ display:block; padding:0 0 3px; }
.packed-window .package .grid { padding:0; }
.packed-window .package-info { background:#f3f3f3; border-bottom:1px solid #ccc; margin:-10px -10px 10px; padding:5px 10px; position:relative; }
.packed-window .package-options { width:60%; }
.packed-window .package-options td,
.packed-window .package-options th { padding:1px 0; }
.packed-window .buttons-set { padding-right:5px; }


/* CATALOG
*******************************************************************************/

/* Category */
.categories-side-col .content-header { padding:0; }
.categories-side-col .content-header h3 { float:none; }
.categories-side-col .content-header button { margin:5px 0 0; }
.categories-side-col .switcher { margin:10px 20px 0 0; }
.categories-side-col .tree-actions { text-align:center; margin:10px 20px 10px 0; }
.categories-side-col .tree-holder { margin-right:20px; }

.no-active-category a span                  { color:#aaa !important; }

#tree-div { overflow:auto!important; padding-bottom:15px; width:200px; }

.x-tree-node { margin:0 !important; }
.x-tree-node .leaf .x-tree-node-icon        { background-image:url(images/fam_leaf.png); }
.x-tree-node .system-leaf .x-tree-node-icon { background-image:url(images/fam_application_form_delete.png); }

.adminhtml-catalog-category-edit .x-tree-node-ct { overflow: visible; }

/* Product - Websites */
.website-name .checkbox                 { vertical-align:top; margin-top:2px; }
.webiste-groups                         { padding:10px 20px; }
.group-stores                           { padding:2px 10px; }

/* Products - Bundles */
.bundle-option-row table tbody td       { white-space:nowrap; }
.bundle-option-row table tbody td label { float:left; }
.bundle-option-row input.option-label   { width:50% !important; }
.bundle-option-row input.option-position{ width:70px !important; }
.catalog-categories .side-col           { width:240px; }

/* Products - Tier Price */
.tier-price-input                       { margin-bottom:8px; }
.tier-price-input input.price           { width:80px; margin-right:10px; }
.tier-price-input input.qty             { width:80px; }
.tier-price-input .tier-container       { position:relative; clear:both; }
.tier-price-input .tier-container div   { float:left; }
.tier-price-input .tier-container label { width:30px; margin:0; padding:0; }
.tier-price-input .validation-advice    { margin:0; height:25px; }
.tier-price-input .custgroup-div select { width:auto; padding:0; }
.tier-price-input .qty-div              { padding-left:20px; }
.tier-price-input .price-div            { padding-left:20px; }
.tier-price-input .price-div .validation-advice { margin:0; }
.btn-remove-tier-group                  { float:right; right:24px; top:5px; }

/* Product - Gallery */
.image-preview                          { position:absolute; cursor:pointer; }

/* Attributes */
.edit-attribute-set .form-list td.label { width:105px; }
.edit-attribute-set  .form-list td.label label { width:105px; }
.edit-attribute-set .entry-edit fieldset input.input-text { width:200px; }
/* Review & Ratings */
.ratings                        { margin:0; }
.rating-box {
    float:left;
    position:relative;
    width:69px;
    height:16px;
    margin:0 5px 3px 0;
    background:url(images/product_rating_blank_star.gif) repeat-x;
    }
.rating-box .rating {
    position:absolute;
    top:0;
    left:0;
    height:16px;
    background:url(images/product_rating_full_star.gif) repeat-x;
    }
.field-row .ratings             { width:120px; float:left; clear:right; }
.field-row .ratings-container   { width:250px; float:left; }
.product-review-box             { width:450px; }
.product-review-box table       { width:100%; }
.product-review-box td,
.product-review-box th          { text-align:center; padding-right:5px; }
.product-review-box td.label    { width:100px; text-align:left; }


/* Price Rules */
.rule-tree ul                   { padding-left:16px !important; border-left:dotted 1px #888; }
.rule-tree .x-tree ul           { padding-left:0 !important; border-left:none !important; }
.rule-param .label              { font-weight:bold; color:black; }
.rule-param .label:hover        { font-weight:bold; color:blue; }
.rule-param .label-disabled     { color:black; cursor:default; text-decoration:none; }
.rule-param .label-disabled:hover { color:black;}
.rule-param .element            { display:none; }
.rule-param input,
.rule-param select              { width:auto !important; min-width:170px; }
.rule-param select.multiselect  { vertical-align:top; }
.rule-param-edit .label         { display:none; }
.rule-param-edit .element       { display:inline; }
.rule-param-add                 { font-weight:normal; color:green; text-decoration:none; }
.rule-param-add:hover           { font-weight:normal; color:blue; text-decoration:none; }
.rule-param-apply               { font-weight:normal; color:green; text-decoration:none; }
.rule-param-apply:hover         { font-weight:normal; color:blue; text-decoration:none; }
.rule-param-remove              { font-weight:normal; color:red; text-decoration:none; }
.rule-param-remove:hover        { font-weight:normal; color:blue; text-decoration:none; }
.rule-chooser                   { border:solid 1px #CCC; margin:5px; padding:5px; display:none; }
.rule-param-wait                { padding-left:20px; background-image:url(images/rule-ajax-loader.gif); background-repeat:no-repeat; background-position:0 50%; }

/* Product Customer Defined options */
.custom-options  .box {padding:0 1.5em; }
.custom-options  .option-box {border:1px solid #cddddd; padding:1em;  background:#e7efef; margin:1.5em 0; }

.custom-options  .option-header {border:0; width:100%; background:#e7efef; border-bottom:1em solid #e7efef; }
.custom-options  .option-header .input-text,
.custom-options  .option-header .select {width:95%; }
.custom-options  .option-header th {padding:2px; }
.custom-options  .option-header td {padding:5px 2px; }

.custom-options .opt-title {width:175px; }
.custom-options .opt-type {width:150px; }
.custom-options .opt-req {width:80px; }
.custom-options .opt-order {width:60px; }

.custom-options  .option-box  .border {width:615px; }

.custom-options th {white-space:nowrap; }
.custom-options  .type-title  {width:auto; }
.custom-options  .type-price  {width:60px; }
.custom-options  .type-type  {width:80px; }
.custom-options  .type-uqty  {width:100px; }
.custom-options  .type-sku  {width:150px; }
.custom-options  .type-order  {width:60px; }
.custom-options  .type-butt  {width:33px; }
.custom-options  .type-last  {width:auto; }

.custom-options .option-box .border input.input-text,
.custom-options .option-box .border select.select { width:90% !important; }

.custom-options .option-box .border .type-last input.input-text { width:60px !important; }
.custom-options .option-box .border input.type-sku  {width:150px !important; }

/* Bundle product */
.bundle .option-box  {padding-bottom:2em; }
.bundle .option-box .border {width:100%; border-bottom:0; }
.bundle .option-box .border td {border-bottom:1px solid #dce5e6!important; }
.bundle .option-title {padding:0 0 10px; border-bottom:1px solid #cddddd; }
.bundle .option-title button {float:right; }
.bundle .option-title label {font-weight:bold; line-height:21px; padding-right:1em; float:left; }
.bundle .option-title .input-text {float:left; width:260px; vertical-align:middle; }
.bundle .option-header {clear:both; margin-top:5px; }

.bundle .border .last {width:33px; }

/* Downloadable Product */
.files { width:195px; }
.files input.input-text { float:left; width:134px !important; }
.files-wide { width:355px; }
.files-wide input.input-text { float:left; width:294px !important; }
.files label,
.files-wide label { float:left; width:55px; }
.files .row,
.files-wide .row { margin-bottom:5px; }
/* Files Uploader */
.files .flex,
.files-wide .flex { float:right !important; position:static !important; }
.files .uploader,
.files-wide .uploader { float:left; overflow:hidden; }
.files .uploader { width:100px; }
.files-wide .uploader { width:260px; }
.files .uploader .file-row-info,
.files-wide .uploader .file-row-info,
.files .uploader .file-row-narrow,
.files-wide .uploader .file-row-narrow { margin:0 !important; }

td.input-price { white-space:nowrap; }
td.input-price .validation-advice { white-space:normal; }
td.input-price input.input-text { width:4em !important; }

input.sort { width:4em !important; }
input.downloads { width:6.5em !important; }

/* CUSTOMER
*******************************************************************************/

/* Addresses */
.address-list                           { width:28em; padding-right:22px; }
.address-list address                   { width:100%; overflow:hidden; }
.address-list .btn-edit-address,
.address-list .btn-remove-address       { position:absolute; top:8px; }
.address-list .btn-edit-address         { right:8px; }
.address-list .btn-remove-address       { right:27px; }
.address-list li                        { position:relative; padding:12px 14px; cursor:pointer; border-top:1px solid #e6e6e6; background:url(images/address_list_li.gif) repeat; }
.address-list li.on                     { background:#e7efef; }
.address-list li.over                   { background-color:#fcf5dd; }
.address-list li table                  { width:100%; }
.delete-address                         { float:right; margin:0 0 10px 10px; }
.address-type .address-type-line        { display:block; margin:2px 0; }
.address-type .address-type-line input  { margin-right:3px; }



/* NEWSLETTER
*******************************************************************************/
.template-preview           { width:100%; height:200px; background-color:#fff; }


/* SYSTEM
*******************************************************************************/
.stores-tree td { padding-top:3px !important; padding-bottom:3px !important; }
.stores-tree td.label label { display:inline; width:auto; padding-right:10px; }
.stores-tree td.website-label label { font-weight:bold; }
.stores-tree td.store-group-label label { font-weight:bold; padding-left:15px; }
.stores-tree td.store-label label { padding-left:30px; }
.stores-tree .buttons-set { margin:10px 0; }

.log-details { border:1px solid #d6d6d6; padding:15px; background:#fafafa; margin:0 0 15px; }
.log-details table { width:100%; }
.log-details table th,
.log-details table td { padding-top:4px; padding-bottom:4px; vertical-align:middle; }
.log-details table th { font-weight:bold; padding-right:30px; white-space:nowrap; }

.sync-indicator { margin-left:5px; margin-right:5px; position:absolute; white-space:nowrap; }
.sync-indicator img,
.sync-indicator span { vertical-align:middle; }

/* Configuration */
/*fieldset.config td          { padding-top:5px; padding-bottom:5px; }
fieldset.config input.input-text { width:250px; }
fieldset.config select.select { width:256px; }*/
div.tree_item,
div.tree_item_last          { background-position:left; background-repeat:no-repeat; padding-left:20px; }
div.tree_item               { background-image:url(images/tree_icons/join.gif); }
div.tree_item_last          { background-image:url(images/tree_icons/joinbottom.gif); }
div.tree_line               { position:absolute; left:0; background-image:url(images/tree_icons/line.gif); }
img.attribute-global        { width:16px; height:16px; vertical-align:middle; }

ul.config-tabs              { border-top:none; }
ul.config-tabs dt {
    border-top:1px solid #849ba3;
    background:#d1dedf url(images/config_tab_dt_bg.gif) no-repeat 0 50%;
    padding:2px 0 2px 1.5em;
    font-weight:bold;
    text-transform:uppercase;
    color:#306375;
    }
ul.config-tabs dl           { margin-bottom:16px; }
ul.config-tabs a.last span  { background-image:none; }

.inline-table { border:0 !important; }
.inline-table td { border:0 !important; padding:0 5px 5px !important; }

.system-fieldset-sub-head td { padding:20px 5px 5px 5px; }
.system-fieldset-sub-head:first-child td { padding:5px 5px 5px 5px; }
.system-fieldset-sub-head h4 { border-bottom:1px solid #ccc; margin:0; }

.comment { padding:5px; }
.comment a { color:#888; margin-left:3px; }

.paypal-payment-notice { font-weight: bold; margin: -10px 0 10px 0; }

.payment-group-title { border:0; padding:10px 5px 10px; }
.payment-group-title strong { display:inline; font-size:14px; border:0; }

/* Config with nested accordion */
.complex > .config { padding:13px 12px 12px; }
.section-config.complex table.form-list { width:100%!important; }
.complex table td:first-child { padding-left:0px!important; }
.complex table td.use-default { width:1px; white-space:nowrap; }

/* Nested accordion */
.section-config .section-config > .config { border:1px solid #d6d6d6; border-top:0; margin:0; padding:15px 12px 20px; background:#fff; }
.section-config .section-config > .entry-edit-head { background:#e6e6e6; border:1px solid #d6d6d6; margin:-1px 0 0; padding:4px 12px; }
.section-config .section-config > .entry-edit-head > a { color:#2f2f2f; background:url(images/bkg_config-nested.png) 100% 5px no-repeat; }
.section-config .section-config > .entry-edit-head > a.open { background-position:100% -195px; }
.section-config .section-config > .entry-edit-head > a:hover {}
.section-config .section-config.active > .entry-edit-head > a.open {}

/* Accordion with configure button */
.with-button { background:#fff; }
.with-button.active > .config { border-bottom-color:#b0b0b0; z-index:1; position:relative; }
.with-button.active:after { background:transparent url(images/bkg_config-shaddow.png) repeat-x 0 0; height:5px; overflow:hidden; position:relative; left:0; margin:0 1px -5px; z-index:2; display:block; content:''; }

tr:last-child .with-button.active > .config { border-bottom-color:#d6d6d6; }
tr:last-child .with-button.active:after { display:none; }
.config-heading { padding:10px 10px 10px 26px; background:#efefef url(images/icon-enabled.png) no-repeat -999em -999em; border:1px solid #d8d8d8; margin:-1px 0 0; }
.enabled .config-heading { background-position:5px 10px; }
.config-heading .button-container { float:right; text-align:right; }
.config-heading .button span { font-size:14px; font-weight:bold; display:block; text-shadow:0 1px 1px #666; text-transform:capitalize; }
.config-heading .button .state-closed { height:auto; }
.config-heading .button .state-opened { height:0; overflow:hidden; }
.config-heading .button.open .state-closed { height:0; overflow:hidden; }
.config-heading .button.open .state-opened { height:auto; }
.config-heading .button:active,
.config-heading .button:active span { outline:0!important; }
.config-heading .heading { float:left; }
.config-heading .heading strong { font-size:12px; display:inline-block; }
.config-heading .heading a { margin-left:8px; font-size:11px; color:#888; font-weight:normal; }
.config-heading .heading a.link-demo:before { content:'|'; display:inline-block; margin-right:8px; color:#888; font-size:12px; }
.config-heading .heading .heading-intro { display:block; font-size:11px; }

td.label label.enabled { background:url(images/icon-enabled.png) no-repeat 100% 1px; min-height:16px; }

/* Advanced nested config */
.section-config .config-advanced > .entry-edit-head { background:none; padding:0 0 0; border:0; }
.section-config .config-advanced > .entry-edit-head a { background:none; padding:0; display:inline-block; border-bottom:1px dotted #f67610; color:#f67610!important; line-height:1.1; white-space:nowrap; }
.section-config .config-advanced > .entry-edit-head a.open,
.section-config .config-advanced > .entry-edit-head a { background:none; }
.section-config .config-advanced > .entry-edit-head a:after { content:''; width:7px; height:5px; background:url(images/bkg_config-advanced.png) no-repeat 0 0; overflow:hidden; display:inline-block; vertical-align:middle; margin-right:-13px; margin-left:5px; }
.section-config .config-advanced > .entry-edit-head a.open:after { background-position:0 -5px; }
.section-config .config-advanced > .config { border:0; padding:15px 0 0;; }

/* Adjusting spacing between form field and nested accordion */
.complex tr.nested + tr[id] > td { padding-top:15px !important; padding-bottom:30px!important; }
.complex tr[id] + tr.nested > td { padding-top:15px !important; }
.pp-buttons-container { white-space:nowrap; float:left; position:relative; z-index:1; margin-right:-200px; }
.pp-buttons-container button { display:inline-block; }
.pp-buttons-container button:first-child { margin:0; }

/* Tooltip for config */
.with-tooltip {}
.field-tooltip { display:inline-block; width:15px; height:15px; position:relative; z-index:1; background:url(images/icon-tooltip.png) no-repeat 0 0; cursor:help; vertical-align:middle; }
.field-tooltip:hover { z-index:100; }
.field-tooltip > span { display:none; }
.field-tooltip:hover > div:hover,
.field-tooltip > div { width:301px; /* width:0; */ background:#f6f6f6 url(images/bkg_tooltip.png) repeat-y 100%; opacity:0; position:absolute; z-index:999; left:-296px; bottom:28px; padding:0 12px; color:#464d50; visibility:hidden; }
.field-tooltip > div:before { position:absolute; top:-5px; left:0; content:''; width:325px; height:5px; overflow:hidden; background:url(images/bkg_tooltip.png) no-repeat 0 0; }
.field-tooltip > div:after { position:absolute; bottom:-14px; left:-1px; content:''; width:325px; height:14px; overflow:hidden; background:url(images/bkg_tooltip.png) no-repeat -325px 0; }

.field-tooltip:hover > div:hover,
.field-tooltip > div {
/*     -moz-transition-property:opacity, visibility, bottom;
    -moz-transition-duration:0.4s, 0s;
    -moz-transition-timing-function:linear;
    -moz-transition-delay:0.3s, 0.7s;
    -webkit-transition-property:opacity, visibility, bottom;
    -webkit-transition-duration:0.4s, 0s;
    -webkit-transition-timing-function:linear;
    -webkit-transition-delay:0.1s, 0.5s;
    -o-transition-property:opacity, visibility, bottom;
    -o-transition-duration:0.4s, 0s;
    -o-transition-timing-function:linear;
    -o-transition-delay:0.1s, 0.5s;
    transition-property:opacity, visibility, bottom;
    transition-duration:0.4s, 0s;
    transition-timing-function:linear;
    transition-delay:0.1s, 0.5s; */
    }

.field-tooltip:hover > div { opacity:1; width:301px; visibility:visible; bottom:30px; }

.field-tooltip:hover > div { opacity:1; width:301px; visibility:visible; bottom:30px;
/*     -moz-transition-duration:0.2s, 0s; -moz-transition-delay:0.1s, 0s;
    -webkit-transition-duration:0.2s, 0s; -webkit-transition-delay:0.1s, 0s;
    -o-transition-duration:0.2s, 0s; -o-transition-delay:0.1s, 0s;
    transition-duration:0.2s, 0s; transition-delay:0.1s, 0s; */
    }


/* Import/export */

#profile-generator select { width:207px; }
#profile-generator input.input-text { width:200px; }
.field-row .with-tip {display:block; margin-left:150px; }
.field-row .with-tip input {float:none; }
.field-row .with-tip small {display:block;padding-top:2px; }

#profile-generator .field-row  button.delete {vertical-align:middle; }
#profile-generator fieldset button.add {display:inline; margin:0; }

/** Product mass attribute update **/

.attribute-change-checkbox { white-space:nowrap; clear:none; margin-left:5px; }

.attribute-change-checkbox label{ margin-left:5px; float:none !important; }

/* PopUp Calendar */
.calendar { z-index:105; }

/** Order view **/
.order-history { width:70%; margin-right:27px; }

ul.super-product-attributes { padding-left:15px; }

/** Media Library **/
.uploader .file-row { width:600px; padding:0.5em 0.6em; margin:0.5em 0.6em; border:1px solid #ccc; background-color:#f9f9f9; /*vertical-align:middle;*/ }
.uploader .file-row-narrow { width: auto; margin: 0 0 2px 40px; }
.uploader .file-row .file-info { float:left; }
/*.uploader .file-row .file-info-name { with: 80%; overflow: hidden; }
.uploader .file-row .file-info-size { width: 20%; }*/
.uploader .file-row-info { margin: 0 0 0 10px; }
.uploader .file-row-info .file-info-name  { font-weight:bold; }
.uploader .file-row .progress-text { float:right; font-weight:bold; }
.uploader .file-row .delete-button { float:right; }
.uploader .progress { border:1px solid #f0e6b7; background-color:#feffcc; }
.uploader .error { border:1px solid #aa1717; background-color:#ffe6de; }
.uploader .error .progress-text { padding-right:10px; }
.uploader .complete { border:1px solid #90c898; background-color:#e5ffed; }

.grid tr.read { background:#fff !important; }
.grid tr.unread { background:#fcf6f5 !important; }
.grid-row-title { color:#444; font-weight:bold; }

.grid-severity-critical,
.grid-severity-critical span,
.grid-severity-major,
.grid-severity-major span,
.grid-severity-minor,
.grid-severity-minor span,
.grid-severity-notice,
.grid-severity-notice span { display:block; height:16px; background-image:url(images/bg_notifications.gif); background-repeat:no-repeat; font:bold 10px/16px Arial, Helvetica, sans-serif; text-transform:uppercase; text-align:center; padding:0 0 0 7px; margin:1px 0; white-space:nowrap; color:#fff; }
.grid-severity-critical { background-position:0 0; }
.grid-severity-critical span { background-position:100% 0; padding:0 7px 0 0; }
.grid-severity-major { background-position:0 -16px; }
.grid-severity-major span { background-position:100% -16px; padding:0 7px 0 0; }
.grid-severity-minor { background-position:0 -32px; }
.grid-severity-minor span { background-position:100% -32px; padding:0 7px 0 0; }
.grid-severity-notice { background-position:0 -48px; }
.grid-severity-notice span { background-position:100% -48px; padding:0 7px 0 0; }

.super-attributes                       { margin:0; padding:0; }
.super-attributes li.attribute          { border:1px solid #dfdfdf; background-color:#ededed; margin:1px 0; }
.super-attributes li.attribute ul.attribute-values { margin:0; padding:0; }
.super-attributes li.attribute div.values-container { width:80%; margin-top:2px; margin-bottom:2px; }
.super-attributes li.attribute-value  { display:block; margin:1px 0; }
.super-attributes li.attribute-value .validation-advice { margin:0; white-space:normal; }
.super-attributes li.attribute-value .attribute-value-label-container { width:200px; }

.super-attributes div.attribute-name-container,
.super-attributes li.attribute div.values-container,
.configurable-simple-product div.values-container,
.super-attributes li.attribute-value .attribute-values-container,
.configurable-simple-product  .attribute-values-container,
.super-attributes li.attribute-value .attribute-values-container-main,
.configurable-simple-product  .attribute-values-container-main,
.super-attributes li.attribute-value .attribute-value-label-container {  display:block;  }

.super-attributes li.attribute-value .attribute-price,
.configurable-simple-product .attribute-price { width:70px !important; }

.super-attributes li.attribute-value .attribute-price-type,
.configurable-simple-product .attribute-price-type {  width:70px !important; }

.super-attributes  div.attribute-name-container {
    cursor:move;
    background-image:url(images/arrow_sort_move.gif);
    background-repeat:no-repeat;
    background-position:4px 50%;
    font-weight:bold;
    padding-left:15px;
    margin-top:2px;
    margin-bottom:2px;
    }


/** Product Gallery Image Previews **/
.preview .cell-image .place-holder { border:1px solid #AEAEAE; width:100px; height:100px; text-align:center; }
.preview .cell-image .place-holder span { margin-top:30px;display:block; }
.preview .cell-position input.input-text { width:90% !important; }
.fieldset-wide .data .preview .cell-position input.input-text  { width:90% !important; }

.tier .data             { width:465px; }
.tier .data select      { width:99%; }
.tier .data input.qty   { width:20px !important; }

.weee .data             { width:465px; }
.weee .data select      { width:99%; }

.giftcard-amounts .data { width:465px; }
.giftcard-amounts .data select { width:99%; }

/* Links */
.link-feed { background:url(images/icon_feed.gif) no-repeat left 2px; padding-left:18px; }

#page-help-link {
    line-height:26px;
    padding-left:20px;
    color:#ebebff;
    background:url(images/fam_help.gif) no-repeat 0 50%;
    }
#page-help-link:link, #page-help-link:visited { text-decoration:none; }
#page-help-link:hover { color:white; }

/* Magento Connect Package Extensions */
.table-editable { border:solid 1px #ccc; background:#fafafa; padding:5px; margin-bottom:5px; }
.table-editable th { border-bottom:solid 1px #ccc; text-align:center; }
.table-editable th, .table-editable td { padding:1px 3px; vertical-align:middle; }
.table-editable select { height:19px; }


/* CMS
*******************************************************************************/
.breadcrumbs { margin: 0 0 10px; }
.breadcrumbs li { display:inline; }
.breadcrumbs li span { margin:0 2px; }

/*table.mceLayout { width:100% !important; }*/

.cms-revision-preview { height:100%; }
.cms-revision-preview iframe { width:100%; height:91%; border:0; }

/* CMS Widgets Instance */
.options-box .option-box { border:1px solid #cddddd; padding:1em; background:#e7efef; margin:1.5em 0; }
.options-box .option-title { padding:0 0 10px; border-bottom:1px solid #cddddd; }
.options-box .option-title button { float:right; }
.options-box .option-title label {font-weight:bold; line-height:21px; padding-right:1em; float:left; }
.options-box .option-title select { float:left; width:260px; vertical-align:middle; }
.options-box .option-header { margin:5px 0 0; width:100%; border:0; background:#e7efef; border-bottom:1em solid #e7efef; }
.options-box .option-header .input-text,
.options-box .option-header select { width:95%; }
.options-box .option-header th { padding:2px; }
.options-box .option-header td { padding:5px 2px; }
.options-box .option-header .tree { margin:5px 0 0; }

/* CMS Popup Window */
.popup-window .magento_message { padding:0 18px; }
.popup-window .content-header { font-family:Arial, Helvetica, sans-serif; padding-top:9px; }
.popup-window .content-header h3 { color:#eb5e00; padding:0; }
.popup-window { height:auto !important; }
.popup-window .grid { position:static; }
.popup-window .table_window td.value2 .grid td,
.popup-window .table_window td.value2 .grid th { padding:2px 4px !important; }
.popup-window .table_window td.value2 .grid tr.headings th { padding:1px 4px 2px !important; }
.popup-window .columns { background-image:none; }
.popup-window .middle { background:none; padding:10px 0; }

.popup-window .uploader .file-row { margin:16px 0; width:auto; }
.popup-window #contents-uploader { margin-bottom:10px; background:#d7e5ef; padding:5px 10px; }
.popup-window #contents-uploader .flex { font-size:0; line-height:0; height:20px; }
.popup-window #contents { margin-left:-3px; height:400px; overflow:auto; position:relative; }
.popup-window #contents .filecnt { float:left; border:1px solid #ccc; cursor:pointer; padding:3px; display:inline; margin:0 0 15px 4px; overflow:hidden; position:relative; width:100px; }
.popup-window #contents .selected { border:1px solid #f1af73; background:#f0f0f0; cursor:default; }
.popup-window #contents .nm { text-align:center; }
.popup-window #contents .nm img { vertical-align:bottom; }

/* Widget Insert */
#widget_window .magento_content { height:auto !important; min-height:450px; }

/* CMS Widget Chooser */
#widget-chooser .columns { background-image:none; }
#widget-chooser .magento_message { padding:10px 18px; }
#widget-chooser .grid th,
#widget-chooser .grid td { padding:2px 4px 2px 4px; }
#widget-chooser .grid tr.filter th { padding-top:5px; padding-bottom:5px; }
#widget-chooser .side-col { padding-top:0.5em; }
#widget-chooser .main-col { padding-right:4px; }

/* CMS Variables Popup */
#variables-chooser .magento_message { padding:10px 18px; }

/* Product description WYSIWYG editor */
#catalog-wysiwyg-editor .buttons-set { margin-top:10px; }
#catalog-wysiwyg-editor .magento_content { height:auto !important; overflow:hidden; }
#catalog-wysiwyg-editor .textarea { width:930px !important; }
#catalog-wysiwyg-editor .magento_message { padding:0 7px; }
#catalog-wysiwyg-editor .magento_buttons { padding-left:7px; padding-right:7px; }

/* Backups */
.backup-dialog { background-color:#6f8992; background:rgba(111, 137, 146, 0.5); cursor:default; left:50%; margin:0 0 0 -271px; position:fixed; top:50%; width:470px; padding:8px; z-index:400; -moz-box-shadow:0 0 100px #ccc; -webkit-box-shadow:0 0 100px #ccc; box-shadow:0 0 100px #ccc; }
.backup-dialog .entry-edit { border:1px solid #6f8992; }
.backup-dialog .content { background:#fff; border-bottom:1px solid #ccc; max-height:400px; overflow:auto; }
.backup-dialog .question {margin-top: 15px;}
.backup-dialog .buttons-set { border-top:1px solid #ddd; background:#eee; margin:0; overflow:hidden; padding:7px 10px 8px; width:448px; }
.backup-dialog .buttons-set button { margin:0 0 0 5px; }
.backup-dialog #ftp-credentials-container {margin-top: 25px;}
.backup-dialog .password-box-container {margin-top: 15px;}
.backup-dialog #ftp-credentials-container fieldset {margin-bottom: 0;}
.backup-dialog input[type=text], .backup-dialog input[type=password] {width: 180px}
.backup-dialog .exclude-media-checkbox-container {margin-top: 15px;}
.backup-dialog td.maintenance-checkbox-container {margin-top: 0; padding-top: 4px;}

/*****************************************/
/******** ALIGNMENTS AND CLEARS **********/
/*****************************************/

/* Directional and spacial */
.f-left, .left      { float:left; }
.f-right, .right    { float:right; }
.v-top              { vertical-align:top; }
.v-middle           { vertical-align:middle !important; }
.v-bottom           { vertical-align:bottom; }
.a-left             { text-align:left !important; }
.a-center           { text-align:center !important; }
.a-right            { text-align:right !important; }
.nm                 { margin:0 !important; }
.np                 { padding:0 !important; }
.no-display         { display:none; }
.no-show            { display:none; }
.nowrap, .nobr      { white-space:nowrap; }
.wrap               { white-space:normal !important; }
.no-float           { float:none !important; }
.pointer            { cursor:pointer; }

/* Color */
.emph, .accent      { color:#eb5e00 !important; }
.subdue             { color:#306375; }

/* Font */
.bold               { font-weight:bold !important; }
.normal             { font-weight:normal !important; }
.strike             { text-decoration: line-through; }

/* Clear */ /* This keeps our HTML free of buncha clearing elements */
.config-heading:after,
.side-col .switcher:after,
.message-popup .message-popup-head:after,
.message-popup .message-popup-content .message:after,
.login-form .form-buttons:after,
.wrapper:after,
.option-title:after,
.columns:after,
.main-col:after,
.content-header-floating:after,
.entry-edit .entry-edit-head:after,
.content-header:after,
.login-box .button-set:after,
ul.tabs-horiz:after,
.header-top:after,
dl.accordion dt:after,
.field-100:after,
.entry-edit fieldset li:after,
.entry-edit fieldset span.field-row:after,
.content:after,
#topnav:after,
.main:after,
.container:after,
.footer:after,
.middle:after,
.header:after,
.box-head:after,
div.actions:after,
.tier-container:after,
.clear:after,
.notification-global:after,
.files .row:after,
.files-wide .row:after,
.grid tr.filter .range .range-line:after,
.store-scope:after { display:block; clear:both; content:"."; font-size:0; line-height:0; height:0; overflow:hidden; }
