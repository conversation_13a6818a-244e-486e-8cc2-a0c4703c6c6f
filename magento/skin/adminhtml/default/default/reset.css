/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/******************************************/
/***************** BASE *******************/
/******************************************/

*                       { margin:0; padding:0; }

body                    { background:#e6e6e6; color:#2f2f2f; font:12px/1.5em Arial, Helvetica, sans-serif;  }

a                       { color:#ea7601; text-decoration:underline; }
a:hover                 { color:#ea7601; text-decoration:underline; }
a img                   { border:0; }
:focus                  { outline:0; }

/* Heading */
h1, h2, h3, h4          { margin-bottom:.5em; line-height:1.4em; }
h2                      { font-size:1.7em; }
h3                      { margin-bottom:.5em; color:#253033; font-size:1.25em; }
h4                      { margin-bottom:.6em; color:#494848; font-size:1.05em; }
h5                      { font-size:1.05em; }
h6                      { font-size:1em; }
h1 a, h1 a:hover, 
h2 a, h2 a:hover, 
h3 a, h3 a:hover,
h4 a, h4 a:hover        { font-weight:normal; }

/* Table */
th                      { padding:0; text-align:left; vertical-align:top; }
td                      { padding:0; vertical-align:top; }

/* Paragraph */
p, address              { margin-bottom:.5em; }
address                 { font-style:normal; }
cite                    { font-style:normal; font-size:10px; }
q:before, 
q:after                 { content:'';}

/* Form */
form                    { display:inline; }
fieldset                { border:0; }
legend                  { display:none; display:block !important; height:0; line-height:0; margin:0; overflow:hidden; padding:0; width:0; visibility:hidden; }
label                   { color:#333; }
input, select           { vertical-align:middle; }
textarea                { overflow:auto; }

/* Lists */
ul,ol                   { list-style:none; }
dt                      { display:block; }

/* Size */
small                   { font-size:.9em; }
big                     { font-size:1.25em; }
