/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

.nav-bar { border-top:1px solid #2d444f; border-bottom:1px solid #2d444f; background:url(images/nav1_bg.gif) repeat-x 0 100% #666e73; padding:0 30px;  }

#nav { float:left; }

/************** ALL LEVELS  *************/ /* Style consistent throughout all nav levels */
#nav li { position:relative; text-align:left; }
#nav li.over { z-index:99; }
#nav li.active { z-index:100; } /* to prevent the li separator from showing through on mouseover on li */
#nav a,
#nav a:hover { display:block; text-decoration:none; }
#nav span { display:block; /*cursor:pointer;*/ }
#nav a { line-height:1.3em; }


/************ 1ST LEVEL  ***************/
#nav li { float:left; background:url(images/nav1_sep.gif) no-repeat 100% 0;  }
#nav li.active { margin-left:-1px; background:url(images/nav1_active.gif) no-repeat; color:#fff; font-weight:bold;  }
#nav li.active em { display:block; position:absolute; top:0; right:-1px; width:3px; height:27px; background:url(images/nav1_active.gif) no-repeat 100% 0; }
#nav a { float:left; padding:0 14px; color:#fff; line-height:27px; }
#nav li.over a { color:#d6e2e5; }


/************ 1ST LEVEL RESET ************/
#nav ul li,
#nav ul li.active { float:none; height:auto; background:none; margin:0; }
#nav ul a,
#nav ul a:hover { float:none; padding:0; line-height:1.3em; }
#nav ul li.over a,
#nav ul li.over a:hover,
#nav ul a,
#nav li.active li { font-weight:normal; }


/************ 2ND LEVEL ************/
#nav ul { position:absolute; width:189px; top:27px; left:-10000px; padding-bottom:3px; border-top:1px solid #2d444f; }
#nav ul ul  { border-top:0; background:url(images/nav3_bg.png) 0 0 no-repeat; padding-top:2px; left:100px; top:13px; }

/* Show menu */
#nav li.over ul { left:-1px; }
#nav li.over ul ul { left:-10000px; }
#nav li.over ul li.over ul { left:100px; }

#nav ul li { background:url(images/nav2_li_bg.png) repeat-y; padding:0 2px; }
#nav ul li a { background:#e3ecee; }
#nav ul li a:hover { background:#d0dfe2; }
#nav li.over ul a,
#nav ul li.active a,
#nav ul li a,
#nav ul li a:hover { color:#2f2f2f; }
#nav ul span,
#nav ul li.last li span { padding:5px 15px; background:url(images/nav2_link_bg.gif) repeat-x 0 100%; }
#nav ul li.last span,
#nav ul li.last li.last span { background:none; }
#nav ul li.last { background:url(images/nav2_last_li_bg.png) no-repeat 0 100%; padding-bottom:3px; }

#nav ul li.parent a,
#nav ul li.parent li.parent a { background-image:url(images/nav2_parent_arrow.gif); background-position:100% 100%; background-repeat:no-repeat; }
#nav ul li.parent li a,
#nav ul li.parent li.parent li a { background-image:none; }

/************ 3RD+ LEVEL ************/
/* Cursors */
#nav li.parent a,
#nav li.parent li.parent a,
#nav li.parent li.parent li.parent a { cursor:default; }

#nav li.parent li a,
#nav li.parent li.parent li a,
#nav li.parent li.parent li.parent li a { cursor:pointer; }

/* Show menu */
#nav ul ul ul { left:-10000px; }
#nav li.over ul li.over ul ul { left:-10000px;}
#nav li.over ul li.over ul li.over ul { left:100px; }

.nav-bar:after { content:"."; display:block; clear:both; font-size:0; line-height:0; height:0; overflow:hidden;  }
