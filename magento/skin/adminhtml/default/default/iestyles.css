/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* Clearing */
.message-popup .message-popup-head,
.message-popup .message-popup-content,
.message-popup .message-popup-content .message,
.login-form .form-buttons,
.wrapper,
.footer,
.option-title,
.columns,
.main-col,
.field-row,
.nav-bar,
ul.tabs-horiz,
dl.accordion dt,
.field-100,
.entry-edit fieldset li,
#nav,
.main,
.middle,
.clear,
.container,
dl.accordion dt,
.grid thead th a,
.content,
.box-head,
.header-top,
ul.tabs,
ul.tabs li,
ul.tabs li span,
ul.tabs li a,
.address-list li,
.entry-edit .entry-edit-head,
.notification-global,
.files .row,
.files-wide .row,
.uploader .file-row,
.grid tr.filter .range .range-line,
.centinel .authentication,
.paypal-payment-notice,
.product-options .options-list li,
.store-scope { zoom:1; }

.clear { display:block; clear:both; height:0; font-size:0; line-height:0; overflow:hidden; }

input.input-file { padding:2px; }
input.checkbox,
input.radio { width:13px; height:13px; }
button.disabled,
button.disabled:hover,
button.disabled:active { filter:alpha(opacity=80); }

/* Complex config fix */
.section-config .section-config { float:left; width:100%; /* position:relative; */ margin-right:-2000px; }
.section-config > .entry-edit-head,
.config-heading { position:relative; z-index:1; zoom:1; }
.config-heading .button .state-closed { height:auto; padding:0; margin:0; position:static; }
.config-heading .button .state-opened { height:0; padding-top:20px; margin-bottom:-20px; position:relative; overflow:hidden; }
.config-heading .button.open .state-closed { height:0; padding-top:20px; margin-top:-20px; position:relative; overflow:hidden; }
.config-heading .button.open .state-opened { height:auto; padding:0; margin:0; position:static; }

.section-config .config-advanced > .entry-edit-head { float:left; background-image:url(images/bkg_config-advanced.png) no-repeat 100% 0; padding-right:10px; }
.section-config .config-advanced > .config { clear:both; }
.section-config .config-advanced > .entry-edit-head a { display:inline; zoom:1; }
.section-config .config-advanced > .entry-edit-head a { //z-index: expression(runtimeStyle.zIndex = 3, insertAdjacentHTML('beforeEnd', '<span class="ieicon"></span>')); }
.section-config .config-advanced .ieicon { width:7px; height:5px; background:url(images/bkg_config-advanced.png) no-repeat 0 0; overflow:hidden; display:inline-block; vertical-align:middle; margin-right:-13px; margin-left:5px; }
.section-config .config-advanced a.open .ieicon { background-position:0 -5px; }
.section-config .use-default label { white-space:nowrap; padding-left:18px!important; zoom:1; }
.section-config .use-default input { position:absolute; z-index:1; margin-top:4px; }
.field-tooltip { display:inline; zoom:1; }
.field-tooltip > div { background:#f6f6f6; border:2px solid #bcbdbe; bottom:20px; padding:12px; }


/* Opacity fix */
#loading-mask { filter:alpha(opacity=80); }
#message-popup-window-mask { filter:alpha(opacity=20); }
.content-header-floating { filter:alpha(opacity=85); }

.message-popup .message-popup-content .message { _height:4.5em; }

#popup-window-mask,
.popup-window-mask { background:#efefef; filter:alpha(opacity=50); }

/* Scalable Button Override */
button, .form-button            { width:auto; margin-left:5px; overflow:visible; }
.massaction button              { width:auto; height: 20px; margin-left:0; overflow:hidden; }

.hor-scroll { overflow-y:hidden; padding-bottom:20px; /*margin-bottom:-16px;*/ }

/* Grid Filter Override */
.grid tr.filter th              { padding-top:3px; padding-bottom:3px; }
.grid tr.filter th input        { margin-right: -10px; }
.grid tr.filter th .range input { margin-right: 2px; }
button.icon-btn                 { width:31px; }

.nested-content .content-header { position:relative; zoom:1; }
dl.accordion dt a,
div.collapseable a               { width:100%; }
.product-image-gallery .grid tbody td { padding: 4px !important; }
.massaction .entry-edit fieldset{ float: right;}
.massaction .entry-edit fieldset  button.scalable {text-align:center !important; padding-left:0;padding-right:0; margin-top:1px;}
.content-header {zoom:1;}

#tree-div {overflow-x:auto !important; height:auto !important; overflow-y:hidden !important; padding-bottom:30px; }

#nav ul li,
#nav ul li.active { float:left; clear:left; width:185px; }
#nav ul a span { zoom:1; }

.grid-severity-critical,
.grid-severity-major,
.grid-severity-minor { margin:3px 0; }
button.add span,
button.delete span { display:inline-block; height:16px;}
