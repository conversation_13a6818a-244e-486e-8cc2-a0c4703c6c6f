/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
.col2-set,
.col3-set,
.col4-set,
.col2-alt-set,
.head,
.page-head,
.page-head-alt,
.header-top-container,
.header-top,
.header-nav-container,
.header-nav,
.quick-access,
#nav,
#nav li,
#nav a,
.middle,
.product-essential,
.button-set,
.actions,
.form-list li,
.button-container,
.ratings,
.page-head,
.page-head-alt,
.group-select li,
.search-autocomplete li,
.side-col li,
.account-box li,
.address-list li,
.listing-type-list .listing-item,
.listing-type-list .product-info .product-reviews,
.account-nav a,
.account-box,
.my-review-detail,
.generic-product-list li { zoom:1; }

.clear { display:block; clear:both; height:0; font-size:0; line-height:0; overflow:hidden; }

input.radio,
input.checkbox { width:13px; height:13px; }
.multi-address-checkout-box .box { zoom:1; }
.multi-address-checkout-box .legend { zoom:1; position:relative; margin-left:8px;}
.quick-access li { padding-right:4px; padding-left:6px;}
.search-autocomplete { left:39px !important; }
.mini-search  { padding-top:-1px; line-height:1em;}
.home-spot { display:inline; }
.mini-related-items .product-details { margin-left:76px; }
.mini-related-items .product-images input { float:left; margin:-4px 2px 0 -4px; }
.mini-related-items .product-images img { float:left; }
#nav ul li.parent { margin-bottom:-3px;}
