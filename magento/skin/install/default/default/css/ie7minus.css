/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

h1#logo { display:inline; }
.form-button, .form-button-alt { margin-bottom:1px; }
.group-select .legend { position:relative; zoom:1; }

/* Min-height for IE */
.login-box .content { height:180px; }
.content-box { height:250px; }
#main { height:400px; }

.validation-advice { height:15px; }
.error-msg, .success-msg, .note-msg { height:23px; }
.currency-switcher h4 { height:21px; }
.base-mini .head h4, .shopping-cart-collaterals h4 { height:16px; }
.login-box h4 { height:16px; }
.login-box .content { height:230px; }
