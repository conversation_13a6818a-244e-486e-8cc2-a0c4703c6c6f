﻿(function($){
    window.Stenik = window.Stenik || {};

    Stenik.escapeSelector = function(string) {
        return string.replace(/[!"#$%&'()*+,.\/:;<=>?@\[\\\]\^`{|}~]/g, '\\$&');
    };

    Stenik.Tabs = function(options) {
        this.initialize(options);
    };

    Stenik.Tabs.prototype = {
        defaultOptions: {
            activeClass: 'selected',
            navItemEvent: 'click',
            navItemInitial: 0,
            contentActivateCallback: null,
            contentDeactivateCallback: null,
            onTabActivateBefore: null,
            onTabActivateAfter: null,
        },
        options: {},
        $tabsWrapper: null,
        $tabContents: null,
        $tabNavItems: null,
        initialize: function(options) {
            this.options = $.extend(true, {}, this.defaultOptions, options);

            this.$tabsWrapper = $(this.options.$tabsWrapper);
            this.$tabContents = $(this.options.$tabContents);
            this.$tabNavItems = $(this.options.$tabNavItems);

            this.$tabsWrapper.data('stenik_tabs', this);

            var tabContentsToAdd = [];
            this.$tabNavItems.each(function() {
                var $el = $(this);

                if ($el.data('tab-target')) {
                    tabContentsToAdd.push($el.data('tab-target'));
                } else if ($el.attr('href') && $el.attr('href') != 'javascript:;') {
                    tabContentsToAdd.push($el.attr('href'));
                }
            });

            for (var i = 0; i < tabContentsToAdd.length; i++) {
                var $tabContent = $(tabContentsToAdd[i]);

                if ($tabContent.length == 0) {
                    $tabContent = $('<div/>');
                }

                this.$tabContents = this.$tabContents.add($tabContent);
            }

            // @todo - by window.localtion.hash
            var numberIsInteger = Number.isInteger = Number.isInteger || function(value) {
              return typeof this.options.navItemInitial === 'number' &&
                isFinite(this.options.navItemInitial) &&
                Math.floor(this.options.navItemInitial) === this.options.navItemInitial;
            };
            if (numberIsInteger) {
                this.openTab(this.options.navItemInitial);
            } else {
                this.openTab(0);
            }

            this.$tabNavItems.on(this.options.navItemEvent, this._navItemActivate.bind(this));
        },
        _navItemActivate: function(event) {
            var $el = $(event.currentTarget);
            var that = this;

            that.$tabNavItems.removeClass(that.options.activeClass);
            that.$tabNavItems.each(function(){
                if ($(this).data("tab-target") === $el.data("tab-target")) {
                    $(this).addClass(that.options.activeClass);
                }
            });

            var tabContentsToActivate = null;

            var windowLocaltionHash = '';

            if ($el.data('tab-target')) {
                // Show tab by target selector
                tabContentsToActivate = $el.data('tab-target');
            } else if ($el.attr('href') && $el.attr('href') != 'javascript:;') {
                // Show tab by href selector
                tabContentsToActivate = $el.attr('href');

                if ($el.attr('href').indexOf('#') === 0) {
                    windowLocaltionHash = $el.attr('href');
                }
            } else {
                // Show tab by position
                var position = this.$tabNavItems.index($el);

                if (position >= 0) {
                    tabContentsToActivate = this.$tabContents.get(position);
                }
            }

            if (tabContentsToActivate) {
                this._deactivateContents(this.$tabContents.not($(tabContentsToActivate)));
                this._activateContents($(tabContentsToActivate));
            }

            if (windowLocaltionHash) {
                window.location.hash = windowLocaltionHash;
            }

            return false;
        },
        _activateContents: function($contents) {

            if (this.options.onTabActivateBefore) {
                this.options.onTabActivateBefore.call($contents, this);
            }

            if (this.options.contentActivateCallback) {
                $contents.each(this.options.contentActivateCallback);
            } else {
                $contents.show();
            }

            this.$tabContents.not($contents).removeClass(this.options.activeClass);
            $contents.addClass(this.options.activeClass);

            if (this.options.onTabActivateAfter) {
                this.options.onTabActivateAfter.call($contents, this);
            }
        },
        _deactivateContents: function($contents) {
            if (this.options.contentDeactivateCallback) {
                $contents.each(this.options.contentDeactivateCallback);
            } else {
                $contents.hide();
            }
        },
        openTab: function(pos) {
            this._navItemActivate({currentTarget: $(this.$tabNavItems.get(pos))});
        }
    }
})(jQuery);