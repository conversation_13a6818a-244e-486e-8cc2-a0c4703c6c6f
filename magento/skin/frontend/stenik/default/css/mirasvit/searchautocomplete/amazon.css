.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  line-height: 0;
}
.clearfix:after {
  clear: both;
}
.hide-text {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.searchautocomplete {
  position: relative;
  float: left;
  width: 560px;
  height: 50px;
  margin-top: 6px;
  background: #fff;
  display: block;
}
.searchautocomplete .sprite {
  background: url('../../../images/mirasvit/sprite.png');
}
.searchautocomplete > label {
  float: left;
  margin-right: 10px;
  color: #333;
  font-weight: bold;
  margin-top: 8px;
}
.searchautocomplete .nav {
  background: url('../../../images/mirasvit/sprite.png');
  float: left;
  position: relative;
  width: 100%;
}
.searchautocomplete .nav .nav-search-in {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 15;
}
.searchautocomplete .nav .nav-search-in .category {
  opacity: 0;
  filter: alpha(opacity=0);
  background: white;
  border: 1px solid #DDD;
  color: black;
  cursor: pointer;
  height: 29px;
  left: 5px;
  margin: 0;
  outline: 0;
  padding: 0;
  position: absolute;
  top: 5px;
  visibility: visible;
  width: auto;
}
.searchautocomplete .nav .nav-search-in .category-fake {
  -webkit-border-radius: 5px 0px 0px 5px;
  -moz-border-radius: 5px 0px 0px 5px;
  border-radius: 5px 0px 0px 5px;
  background-color: #fcfcfc;
  background-image: -moz-linear-gradient(top, #ffffff, #f7f7f7);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f7f7f7));
  background-image: -webkit-linear-gradient(top, #ffffff, #f7f7f7);
  background-image: -o-linear-gradient(top, #ffffff, #f7f7f7);
  background-image: linear-gradient(to bottom, #ffffff, #f7f7f7);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff7f7f7', GradientType=0);
  border-right: 1px solid #dddddd;
  color: #777;
  cursor: pointer;
  float: left;
  font-size: 11px;
  height: 27px;
  line-height: 27px;
  margin: 4px 0px 0px 2px;
  padding: 0px 25px 0px 10px;
  text-align: center;
  white-space: nowrap;
}
.searchautocomplete .nav .nav-search-in .nav-down-arrow {
  right: 10px;
  top: 16px;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 4.5px 0 4.5px;
  border-color: #000000 transparent transparent transparent;
  line-height: 0px;
}
.searchautocomplete .nav .nav-search-in:hover .category-fake {
  background-color: #eeeeee;
  background-image: -moz-linear-gradient(top, #f7f7f7, #e1e1e1);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f7f7f7), to(#e1e1e1));
  background-image: -webkit-linear-gradient(top, #f7f7f7, #e1e1e1);
  background-image: -o-linear-gradient(top, #f7f7f7, #e1e1e1);
  background-image: linear-gradient(to bottom, #f7f7f7, #e1e1e1);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff7f7f7', endColorstr='#ffe1e1e1', GradientType=0);
}
.searchautocomplete .nav .nav-search-in:hover .nav-down-arrow {
  background-position: -6px -34px;
}
.searchautocomplete .nav .nav-input {
  *zoom: 1;
  margin: 0;
  padding: 0;
  z-index: 12;
  position: relative;
}
.searchautocomplete .nav .nav-input:before,
.searchautocomplete .nav .nav-input:after {
  display: table;
  content: "";
  line-height: 0;
}
.searchautocomplete .nav .nav-input:after {
  clear: both;
}
.searchautocomplete .nav .nav-input input {
  float: left;
  width: 100%;
  height: 50px;
  padding: 0px 58px 0px 20px;
  margin: 0px;
  color: #000;
  font-size: 18px;
  font-weight: normal;
  text-decoration: none;
  background: #fff;
  border: 1px solid #d9dbda;
  box-sizing: border-box;
  position: relative;
  z-index: 15;
  font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
}
.searchautocomplete .nav .nav-input input:hover{border-color: #777;}
.searchautocomplete .nav .nav-input input:focus{border-color: #777; background: #ebebeb;}
.searchautocomplete .nav .nav-input input.suggest {
  display: none;
  color: #999;
  z-index: 9;
}
.searchautocomplete button.button{
  width: 49px;
  height: 48px;
  position: absolute;
  right: 1px;
  top: 1px;
  z-index: 999;
  min-width: auto;
  background: #f8f8f8 url(../../../images/searchSubmitIcon.png) no-repeat;
  background-position: center center;
  border-left: 1px solid #d9dbda;
}
.searchautocomplete button.button:hover {
  background: #ebebeb url(../../../images/searchSubmitIcon.png) no-repeat;
  background-position: center center;
}
.searchautocomplete .nav-submit-button {
  background: url('../../../images/mirasvit/sprite.png');
  background-position: 0px -34px;
  background-repeat: no-repeat;
  float: left;
  height: 34px;
  padding-left: 5px;
}
.searchautocomplete .nav-submit-button .button {
  width: 39px;
  height: 34px;
  padding: 0px 9px;
  margin: 0px;
  cursor: pointer;
  font-weight: bold;
  color: white;
  line-height: 12px;
  font-size: 13px;
  background-color: #3c454e;
  background-image: -moz-linear-gradient(top, #444c55, #313a44);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#444c55), to(#313a44));
  background-image: -webkit-linear-gradient(top, #444c55, #313a44);
  background-image: -o-linear-gradient(top, #444c55, #313a44);
  background-image: linear-gradient(to bottom, #444c55, #313a44);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff444c55', endColorstr='#ff313a44', GradientType=0);
  border: 1px solid #313a44;
  -webkit-border-radius: 0px 5px 5px 0px;
  -moz-border-radius: 0px 5px 5px 0px;
  border-radius: 0px 5px 5px 0px;
  z-index: 14;
}
.searchautocomplete .nav-submit-button .button:hover {
  background-color: #39414b;
  background-image: -moz-linear-gradient(top, #313a44, #444c55);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#313a44), to(#444c55));
  background-image: -webkit-linear-gradient(top, #313a44, #444c55);
  background-image: -o-linear-gradient(top, #313a44, #444c55);
  background-image: linear-gradient(to bottom, #313a44, #444c55);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff313a44', endColorstr='#ff444c55', GradientType=0);
}
.searchautocomplete .searchautocomplete-placeholder {
  background: #FFF;
  border: 1px solid #ededed;
  padding: 0;
  position: absolute;
  right: 0px;
  top: 49px;
  width: 100%;
  z-index: 1000;
}
.searchautocomplete .searchautocomplete-placeholder:before {
  border-color: transparent transparent #CCC transparent;
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 5px;
  height: 0;
  width: 0;
  top: -11px;
  left: 30px;
  display: none;
}
.searchautocomplete .searchautocomplete-placeholder ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.searchautocomplete .searchautocomplete-placeholder ul li {
  *zoom: 1;
  text-align: left;
  padding: 5px 5px;
  margin-bottom: 0px;
  border-bottom: 1px solid #e7e7e7;
}
.searchautocomplete .searchautocomplete-placeholder ul li:before,
.searchautocomplete .searchautocomplete-placeholder ul li:after {
  display: table;
  content: "";
  line-height: 0;
}
.searchautocomplete .searchautocomplete-placeholder ul li:after {
  clear: both;
}
.searchautocomplete .searchautocomplete-placeholder ul li a {
  text-decoration: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li a.name {
  color: #616161;
  display: block;
  margin: 40px 0px 5px 0px;
}
.searchautocomplete .searchautocomplete-placeholder ul li a .pull-right {
  float: right;
}
.searchautocomplete .searchautocomplete-placeholder ul li.active {
  background-color: #f9f9f9;
  cursor: pointer;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
}
.searchautocomplete .searchautocomplete-placeholder ul li.active a {
  text-decoration: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li .searchautocomlete-image {
  float: left;
  margin: 5px 10px 0px 0px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box {
  font-size: 16px;
  color: #cc1153;
  float: left;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .regular-price .price-label,
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .old-price .price-label,
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .special-price .price-label {
  display: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .price-label {
  font-weight: normal;
  color: #999;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .old-price .price {
  font-size: 0.9em;
  color: #999;
}
.searchautocomplete .searchautocomplete-placeholder ul li .highlight strong {
  color: #616161;
  font-weight: bold;
}
.searchautocomplete .searchautocomplete-placeholder ul li .ratings {
  margin: 0;
  line-height: 14px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .ratings .rating-box {
  float: left;
  margin: 0 5px 0 0;
}
.searchautocomplete .searchautocomplete-placeholder .index-title {
  text-align: right;
  color: #0A263C;
  border-bottom: 1px solid #CCC;
  padding: 2px 5px;
}
.searchautocomplete .searchautocomplete-placeholder .index-title span {
  color: #666;
  font-size: 0.9em;
}
.searchautocomplete .searchautocomplete-placeholder .all {
  text-align: center;
  margin: 15px 0px;
}
.searchautocomplete .searchautocomplete-placeholder .all a{
  color: #cc1159;
}
.searchautocomplete .searchautocomplete-loader {
  position: absolute;
  width: 18px;
  height: 18px;
  top: 9px;
  right: 10px;
  z-index: 99;
  -webkit-transform: scale(0.6);
  -moz-transform: scale(0.6);
  -ms-transform: scale(0.6);
  -o-transform: scale(0.6);
  transform: scale(0.6);
  display: none;
}
.searchautocomplete .searchautocomplete-loader div {
  position: absolute;
  background-color: #FFFFFF;
  height: 3px;
  width: 3px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-animation-name: f_autocomplete_loader;
  animation-name: f_autocomplete_loader;
  -webkit-animation-duration: 0.64s;
  animation-duration: 0.64s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-direction: normal;
  animation-direction: normal;
}
.searchautocomplete .searchautocomplete-loader div#g01 {
  left: 0px;
  top: 7px;
  -webkit-animation-delay: 0.24s;
  animation-delay: 0.24s;
}
.searchautocomplete .searchautocomplete-loader div#g02 {
  left: 2px;
  top: 2px;
  -webkit-animation-delay: 0.32s;
  animation-delay: 0.32s;
}
.searchautocomplete .searchautocomplete-loader div#g03 {
  left: 7px;
  top: 0px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}
.searchautocomplete .searchautocomplete-loader div#g04 {
  right: 2px;
  top: 2px;
  -webkit-animation-delay: 0.48s;
  animation-delay: 0.48s;
}
.searchautocomplete .searchautocomplete-loader div#g05 {
  right: 0px;
  top: 7px;
  -webkit-animation-delay: 0.56s;
  animation-delay: 0.56s;
}
.searchautocomplete .searchautocomplete-loader div#g06 {
  right: 2px;
  bottom: 2px;
  -webkit-animation-delay: 0.64s;
  animation-delay: 0.64s;
}
.searchautocomplete .searchautocomplete-loader div#g07 {
  left: 7px;
  bottom: 0px;
  -webkit-animation-delay: 0.72s;
  animation-delay: 0.72s;
}
.searchautocomplete .searchautocomplete-loader div#g08 {
  left: 2px;
  bottom: 2px;
  -webkit-animation-delay: 0.8s;
  animation-delay: 0.8s;
}
@-moz-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-webkit-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-ms-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-o-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
.searchautocomplete-widget {
  position: relative;
  top: auto;
  left: auto;
  bottom: auto;
  right: auto;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}
