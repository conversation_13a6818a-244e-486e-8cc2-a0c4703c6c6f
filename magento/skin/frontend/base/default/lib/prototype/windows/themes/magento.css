/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
.dialog { border:1px solid #555; }
.dialog .bot { display:none !important; }
.overlay_magento { background-color:#000; filter:alpha(opacity=60); -moz-opacity:.6; opacity:.6; -webkit-opacity:.6; }
.top.table_window { border-bottom:1px solid #e6e6e6; background:#6a838b url(magento/top_bg.gif) 0 100% repeat-x; }

.magento_nw { width:6px; height:28px; }
.magento_n { height:28px; }
.magento_ne { width:6px; height:28px; }

.magento_w { width:6px; }
.magento_e { width:6px; }
.magento_w,
.magento_e,
.magento_content { background: #fafafa url(magento/content_bg.gif) 0 0 repeat-x; }

.magento_sw { background:#deebf0; width:5px; height:3px; }
.magento_s { background:#deebf0; height:3px; }
.magento_se,

.magento_sizer { background:#deebf0; width:5px; height:3px; }
.magento_sizer { cursor:se-resize; }

.magento_close { width:16px; height:16px; background:url(magento/window_close.png) no-repeat 0 0; position:absolute; top:5px; right:7px; cursor:pointer; z-index:1000; }
.magento_minimize { width:16px; height:16px; background:url(magento/window_minimize.png) 0 0 no-repeat; position:absolute; top:5px; right:28px; cursor:pointer; z-index:1000; }
.magento_maximize { width:16px; height:16px; background:url(magento/window_maximize.png)0 0 no-repeat; position:absolute; top:5px; right:49px; cursor:pointer; z-index:1000; }

.magento_title { float:left; width:100%; font:bold 12px/28px Arial, Helvetica, sans-serif; color:#fff; text-align:left; }

.magento_content { overflow:auto; font-size:12px; }
.magento_content,
.magento_content label { color:#333; font-family:Arial, sans-serif; }

.magento_buttons { padding:10px; text-align:right; }
.magento_buttons input.button { border-width:1px; border-style:solid; border-color:#ed6502 #a04300 #a04300 #ed6502;  background:#ffac47 url(magento/btn_bg.gif) 0 100% repeat-x; padding:0 7px 1px 7px; font:bold 12px/18px Arial, Helvetica, sans-serif; color:#fff; cursor:pointer; text-align:center; white-space:nowrap; }
.magento_buttons button.button span span span { display:inline; background:none; padding:0; height:auto; }

/* FOR IE */
* html .magento_close { background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="../themes/magento/window_maximize.png", sizingMethod="crop"); }
* html .magento_minimize { background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="../themes/magento/window_close.png", sizingMethod="crop");}
* html .magento_maximize { background-image:none; filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src="../themes/magento/window_minimize.png", sizingMethod="crop"); }
