.mstcore-toolbar {
    table {
        max-width: 100%;
        background-color: #fff;
        border-collapse: collapse;
        border-spacing: 0;
        width: 100%;
        margin-bottom: 18px;
        // Cells
        th,
        td {
            padding: 8px;
            line-height: 18px;
            text-align: left;
            vertical-align: top;
            border-top: 1px solid #eee;
        }
        th {
            font-weight: bold;
        }
        // Bottom align for column headings
        thead th {
            vertical-align: bottom;
        }
        // Remove top border from thead by default
        caption + thead tr:first-child th,
        caption + thead tr:first-child td,
        colgroup + thead tr:first-child th,
        colgroup + thead tr:first-child td,
        thead:first-child tr:first-child th,
        thead:first-child tr:first-child td {
            border-top: 0;
        }
        // Account for multiple tbody instances
        tbody + tbody {
            border-top: 2px solid #eee;
        }

        // Nesting
        .table {
            background-color: #fff;
        }

        tbody {
            tr:hover > td,
            tr:hover > th {
                background-color: #eee;
            }
        }
        th, td {
        padding: 4px 5px;
        }
    }

    .green {
            color: #0a0;
    }

    .red {
            color: #a00;
    }

    .yellow {
            color: #aa0;
    }

    .panel-label {
        padding: 5px 25px;
        font-weight: bold;
        background: #fff;
        border: 1px solid #000;
        float: left;
        margin: 10px;
        cursor: pointer;
    }

    .panel-container {
        display: none;
    }

    .clear {
        height: 0px;
        clear: both;
    }

    .panel-container {
        background: #fff;
        margin: 10px;
        padding: 15px;
    }
}