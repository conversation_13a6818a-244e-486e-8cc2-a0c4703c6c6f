.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  line-height: 0;
}
.clearfix:after {
  clear: both;
}
.hide-text {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.searchautocomplete .search-autocomplete {
  position: absolute;
}
.searchautocomplete .form-search {
  overflow: none !important;
  position: relative;
}
.searchautocomplete .sprite {
  background: url('../../../images/mirasvit/sprite.png');
}
.searchautocomplete .nav-search-in {
  position: absolute;
  top: 2px;
  left: 0px;
  z-index: 11;
}
.searchautocomplete .nav-search-in .category {
  opacity: 0;
  filter: alpha(opacity=0);
  background: white;
  border: 1px solid #DDD;
  color: black;
  cursor: pointer;
  height: 29px;
  left: 0;
  margin: 0;
  outline: 0;
  padding: 0;
  position: absolute;
  top: 0;
  visibility: visible;
  width: auto;
}
.searchautocomplete .nav-search-in .category-fake {
  height: 20px;
  color: #777;
  cursor: pointer;
  float: left;
  font-size: 11px;
  padding: 0px 25px 0px 10px;
  text-align: center;
  white-space: nowrap;
  margin-top: 6px;
}
.searchautocomplete .nav-search-in .nav-down-arrow {
  right: 10px;
  top: 16px;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 4.5px 0 4.5px;
  border-color: #000000 transparent transparent transparent;
  line-height: 0px;
}
.searchautocomplete .nav-search-in:hover .category-fake {
  color: #000;
}
.searchautocomplete .nav-search-in:hover .nav-down-arrow {
  background-position: -6px -34px;
}
.searchautocomplete .searchautocomplete-placeholder {
  -webkit-border-radius: 5px 5px 5px 5px;
  -moz-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  background: #FFF;
  border: 1px solid #ccc;
  position: absolute;
  z-index: 102;
  box-shadow: 0 10px 10px #5E5E5E;
  top: 30px;
}
.searchautocomplete .searchautocomplete-placeholder:before {
  border-color: transparent transparent #CCC transparent;
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 5px;
  height: 0;
  width: 0;
  top: -11px;
  left: 30px;
}
.searchautocomplete .searchautocomplete-placeholder ul {
  list-style: none;
  margin: 0;
  border: none!important;
  background: none!important;
  box-shadow: none;
  position: relative;
  padding: 5px;
}
.searchautocomplete .searchautocomplete-placeholder ul li {
  *zoom: 1;
  text-align: left;
  padding: 5px 5px;
  border: 1px solid #e2e2e2;
  margin-bottom: 5px;
}
.searchautocomplete .searchautocomplete-placeholder ul li:before,
.searchautocomplete .searchautocomplete-placeholder ul li:after {
  display: table;
  content: "";
  line-height: 0;
}
.searchautocomplete .searchautocomplete-placeholder ul li:after {
  clear: both;
}
.searchautocomplete .searchautocomplete-placeholder ul li a {
  text-decoration: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li a .pull-right {
  float: right;
}
.searchautocomplete .searchautocomplete-placeholder ul li.active {
  background-color: #eeffee;
  box-shadow: 0 0 1px #525252;
  cursor: pointer;
}
.searchautocomplete .searchautocomplete-placeholder ul li.active a {
  text-decoration: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li .searchautocomlete-image {
  float: left;
  margin: 0px 10px 5px 0px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box {
  font-weight: bold;
  font-size: 13px;
  color: #C76200;
  float: right;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .regular-price .price-label,
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .old-price .price-label,
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .special-price .price-label {
  display: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .price-label {
  font-weight: normal;
  color: #999;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .old-price .price {
  font-size: 0.9em;
  color: #999;
}
.searchautocomplete .searchautocomplete-placeholder ul li .ratings {
  margin: 0;
  line-height: 14px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .ratings .rating-box {
  float: left;
  margin: 0 5px 0 0;
}
.searchautocomplete .searchautocomplete-placeholder .index-title {
  text-align: right;
  color: #0A263C;
  border-bottom: 1px solid #CCC;
  padding: 2px 5px;
  background-color: #F7F7F6;
}
.searchautocomplete .searchautocomplete-placeholder .index-title span {
  color: #666;
  font-size: 0.9em;
}
.searchautocomplete .searchautocomplete-placeholder .all {
  text-align: right;
  margin: 10px 10px 5px 10px;
}
.searchautocomplete .searchautocomlete-image {
  float: left;
  margin: 0px 5px 5px 0px;
}
.searchautocomplete .searchautocomplete-loader {
  position: absolute;
  width: 18px;
  height: 18px;
  top: 11px;
  right: 44px;
  z-index: 99;
  -webkit-transform: scale(0.6);
  -moz-transform: scale(0.6);
  -ms-transform: scale(0.6);
  -o-transform: scale(0.6);
  transform: scale(0.6);
  display: none;
}
.searchautocomplete .searchautocomplete-loader div {
  position: absolute;
  background-color: #FFFFFF;
  height: 3px;
  width: 3px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-animation-name: f_autocomplete_loader;
  animation-name: f_autocomplete_loader;
  -webkit-animation-duration: 0.64s;
  animation-duration: 0.64s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-direction: normal;
  animation-direction: normal;
}
.searchautocomplete .searchautocomplete-loader div#g01 {
  left: 0px;
  top: 7px;
  -webkit-animation-delay: 0.24s;
  animation-delay: 0.24s;
}
.searchautocomplete .searchautocomplete-loader div#g02 {
  left: 2px;
  top: 2px;
  -webkit-animation-delay: 0.32s;
  animation-delay: 0.32s;
}
.searchautocomplete .searchautocomplete-loader div#g03 {
  left: 7px;
  top: 0px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}
.searchautocomplete .searchautocomplete-loader div#g04 {
  right: 2px;
  top: 2px;
  -webkit-animation-delay: 0.48s;
  animation-delay: 0.48s;
}
.searchautocomplete .searchautocomplete-loader div#g05 {
  right: 0px;
  top: 7px;
  -webkit-animation-delay: 0.56s;
  animation-delay: 0.56s;
}
.searchautocomplete .searchautocomplete-loader div#g06 {
  right: 2px;
  bottom: 2px;
  -webkit-animation-delay: 0.64s;
  animation-delay: 0.64s;
}
.searchautocomplete .searchautocomplete-loader div#g07 {
  left: 7px;
  bottom: 0px;
  -webkit-animation-delay: 0.72s;
  animation-delay: 0.72s;
}
.searchautocomplete .searchautocomplete-loader div#g08 {
  left: 2px;
  bottom: 2px;
  -webkit-animation-delay: 0.8s;
  animation-delay: 0.8s;
}
@-moz-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-webkit-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-ms-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-o-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
.searchautocomplete .input-text {
  outline: none;
}
.searchautocomplete.active .input-text {
  outline: none;
  border-radius: 2px 2px 0px 0px;
  border: 1px solid #3399cc;
}
.searchautocomplete .nav-search-in {
  top: 0px;
}
.searchautocomplete .nav-search-in .category-fake {
  padding-top: 7px;
}
.searchautocomplete .nav-search-in .nav-down-arrow {
  top: 19px;
}
.searchautocomplete .nav-search-in .category {
  top: 0px;
  left: 0px;
  height: 40px;
}
.searchautocomplete .searchautocomplete-placeholder {
  top: 0px !important;
  box-shadow: 0 3px 4px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 5px 5px;
  border: 1px solid #3399cc;
  border-top: 0px;
}
.searchautocomplete .searchautocomplete-placeholder:before {
  display: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li {
  border: 1px solid #fff;
}
.searchautocomplete .searchautocomplete-placeholder ul li div.highlight {
  color: #666;
}
.searchautocomplete .searchautocomplete-placeholder ul li img {
  border: 1px solid #ededed;
}
.searchautocomplete .searchautocomplete-placeholder ul li:last-child {
  border: 1px solid #fff;
}
.searchautocomplete .searchautocomplete-placeholder ul li.active {
  background: transparent;
  box-shadow: none;
  border: 1px solid #3399cc !important;
}
.searchautocomplete .searchautocomplete-placeholder ul li .ratings {
  margin: 5px 0px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .name {
  white-space: nowrap;
  overflow: hidden;
  font-size: 14px;
  line-height: 16px;
  display: block;
}
.searchautocomplete .searchautocomplete-placeholder ul li div.highlight {
  display: none;
}
@media only screen and (min-width: 1100px) {
  .searchautocomplete .searchautocomplete-placeholder ul li div.highlight {
    display: block;
    float: left;
    max-width: 250px;
  }
}
.searchautocomplete .searchautocomplete-placeholder ul li .searchautocomlete-image {
  margin: 0px 10px 0px 0px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box {
  margin: 0px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .price {
  font-size: 13px;
}
@media only screen and (min-width: 771px) {
  #header-search {
    width: 35% !important;
  }
}
