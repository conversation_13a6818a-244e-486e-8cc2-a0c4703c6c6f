/**
 * Inc<PERSON>o is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */

div.inchoo-socialconnect-linkedin{
    float: right;
    height: 30px !important;
    background: transparent url('../images/login-button-left.png') no-repeat top left !important;
}

div.inchoo-socialconnect-linkedin-inner{
    height: 30px !important;
    float: left !important;
    margin-left: 30px !important;
    background: transparent url('../images/login-button-inner.png') repeat-x top left !important;
    padding: 0 5px !important;
    text-shadow: 1px 1px #1c376d;
}

div.inchoo-socialconnect-linkedin-right{
    float: left !important;
    height: 30px !important;
    width: 3px !important;
    background: transparent url('../images/login-button-right.png') no-repeat top left !important;
}

div.inchoo-socialconnect-linkedin-inner a{
    color: #FFFFFF !important;
    padding: 0 !important;
    margin: 0 !important;
    text-decoration: none !important;
    line-height: 30px !important;
}      
