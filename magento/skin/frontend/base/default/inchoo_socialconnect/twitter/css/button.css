/**
 * Inc<PERSON>o is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */

div.inchoo-socialconnect-twitter{
    float: right;
    height: 30px !important;
    background: transparent url('../images/login-button-left.png') no-repeat top left !important;
}

div.inchoo-socialconnect-twitter-inner{
    height: 30px !important;
    float: left !important;
    margin-left: 30px !important;
    background: transparent url('../images/login-button-inner.png') repeat-x top left !important;
    padding: 0 5px !important;
    text-shadow: 1px 1px #00a1c4;
}

div.inchoo-socialconnect-twitter-right{
    float: left !important;
    height: 30px !important;
    width: 3px !important;
    background: transparent url('../images/login-button-right.png') no-repeat top left !important;
}

div.inchoo-socialconnect-twitter-inner a{
    color: #FFFFFF !important;
    padding: 0 !important;
    margin: 0 !important;
    text-decoration: none !important;
    line-height: 30px !important;
}    
