(function($) {
    window.Stenik = window.Stenik || {};

    window.Stenik.Checkout = Class.create();
    window.Stenik.Checkout.prototype = {
        currentAjax: false,
        savingStep: false,
        nextStep: false,
        initialize: function(saveUrl){
            this.saveUrl = saveUrl;
            this.steps = [];
        },
        addStep: function(stepCode) {
            this.steps.push(stepCode);

            if (this.steps.length == 1) {
                $('#sc-checkout-step-' + stepCode).show();
            } else {
                $('#sc-checkout-step-' + stepCode).hide();
            }


            var $stepWrapper = $('#sc-checkout-step-' + stepCode);
            this.initSections($stepWrapper);
        },
        initSections: function($wrapper) {
            var checkout = this;

            $wrapper.find('.stenik-onepage-section').add($wrapper).filter('.stenik-onepage-section').each(function() {
                var $section = $(this);
                var sectionCode = $section.data('section-code');
                var sectionGroup = $section.data('section-group');
                var autosubmitDisabled = ($section.data('section-disable-auto-submit') == 1);

                if (sectionGroup && !autosubmitDisabled) {
                    $section.find('input,select,textarea').keyup(function() {
                        var timeout = $section.data('stenikCheckoutSection_validateAndSaveSection_timeout');

                        if (timeout) {
                            clearTimeout(timeout);
                        }

                        timeout = setTimeout(function() {
                            checkout.validateAndSaveSection(sectionCode);
                        }, 2000);
                        $section.data('stenikCheckoutSection_validateAndSaveSection_timeout', timeout);
                    });

                    $section.find('input,select,textarea').change(function() {
                        /*
                            The timeout is needed to let other scripts do their stuff before the values gathering and validation.
                         */

                        var timeout = $section.data('stenikCheckoutSection_validateAndSaveSection_timeout');

                        if (timeout) {
                            clearTimeout(timeout);
                        }

                        timeout = setTimeout(function() {
                            checkout.validateAndSaveSection(sectionCode);
                        }, 60);
                        $section.data('stenikCheckoutSection_validateAndSaveSection_timeout', timeout);
                    });
                }
            });
        },
        validateAndSaveSection: function(sectionCode) {
            var checkout = this;
            var $section = $('#stenik-onepage-section-' + sectionCode);

            if (!$section.length) {
                return;
            }

            var sectionGroup = $section.data('section-group');

            if (!sectionGroup) {
                return;
            }

            var affectGroups = $section.data('section-affect-groups');

            var timeout = $section.data('stenikCheckoutSection_validateAndSaveSection_timeout');

            if (timeout) {
                clearTimeout(timeout);
            }

            $sectionGroupFields = $('.stenik-onepage-section[data-section-group="' + sectionGroup + '"]').find('input,select,textarea');

            // Save whole section group. If not - save the section
            if (checkout.areFieldsValid($sectionGroupFields)) {
                var data = checkout.serializeFields($sectionGroupFields)
                if (affectGroups) {
                    data['refresh_section_groups'] = affectGroups;
                }
                checkout.saveData(data);
            } else {
                $sectionFields = $section.find('input,select,textarea');
                $sectionFieldsToValidate = $sectionFields;

                var isPartial = false;

                var affectGroupsOnFilledFields = $section.data('section-affect-groups-on-filled-fields');
                if (affectGroupsOnFilledFields) {
                    affectGroupsOnFilledFields = affectGroupsOnFilledFields.split(',');

                    var affectGroupsOnFilledFieldIds = [];
                    for (var i = affectGroupsOnFilledFields.length - 1; i >= 0; i--) {
                        affectGroupsOnFilledFieldIds.push('#' + Stenik.escapeSelector(affectGroupsOnFilledFields[i]));
                    }

                    $sectionFieldsToValidate = $section.find(affectGroupsOnFilledFieldIds.join(','));
                    isPartial = true;
                }

                if (checkout.areFieldsValid($sectionFieldsToValidate)) {
                    var data = checkout.serializeFields($sectionFields)

                    if (affectGroups) {
                        data['refresh_section_groups'] = affectGroups;
                    }

                    if (isPartial) {
                        data['partial_section_update'] = 1;
                    }

                    checkout.saveData(data);
                }
            }
        },
        save: function(stepCode){
            if (this.savingStep) {
                return;
            }

            var $stepWrapper = $('#sc-checkout-step-' + stepCode);

            var validated = true;

            $stepWrapper.find('form').each(function() {
                var validator = new Validation(this);
                validated = validated && validator.validate();
            });

            if (validated) {
                var data = {};
                var datas = $stepWrapper.find('input,select,textarea').serializeArray();
                for (var i = 0; i < datas.length; i++) {
                    data[datas[i].name] = datas[i].value;
                }

                var useNextStep = false;
                for (var i = 0; i < this.steps.length; i++) {
                    if (useNextStep) {
                        this.nextStep = this.steps[i];
                        break;
                    }

                    if (this.steps[i] == stepCode) {
                        useNextStep = true;
                    }
                }

                var checkout = this;
                this.abortSaving();
                this.showContainerOverlay($stepWrapper);
                this.savingStep = true;
                data.random = Math.random();
                this.saveData(data);

                if (this.currentAjax) {
                    this.currentAjax.always(function() {
                        checkout.savingStep = false;
                    });
                } else {
                    checkout.savingStep = false;
                }
            }
        },
        lastSavedData: false,
        saveData: function(data) {
            var dataJson = JSON.stringify(data);

            if (dataJson === this.lastSavedData) {
                this.hideLoaders();
                return;
            }

            var checkout = this;

            this.abortSaving();

            if (data.refresh_section_groups) {
                var $sections = $.fn;
                var sectionGroups = data.refresh_section_groups.split(',');
                for (var i = sectionGroups.length - 1; i >= 0; i--) {
                    $sections = $sections.add($('.stenik-onepage-section[data-section-group="' + sectionGroups[i] + '"]'));
                }

                $sections.each(function() {
                    checkout.showContainerOverlay(this);
                });
            }

            this.currentAjax = $.ajax({
                url: this.saveUrl,
                method: 'POST',
                data: data,
                dataType: 'json'
            }).done(function() {
                checkout.onSave.apply(checkout, arguments);
                checkout.lastSavedData = dataJson;
            }).fail(function() {
            }).always(function() {
                checkout.currentAjax = false;
                checkout.nextStep = false;
                checkout.hideLoaders();
            });
        },
        abortSaving: function() {
            if (this.currentAjax) {
                this.currentAjax.abort();
                this.currentAjax = false;
                this.hideLoaders();
            }
        },
        serializeFields: function($fields) {
            var $fields = $($fields);
            var data = {};

            /*
             * jQuery skips selects without options/value -
             * force empty string value for each
             */
            $fields.filter('select').each(function() {
                if (!$(this).val()) {
                    $(this).val('');
                }
            });

            var serializedData = $fields.serializeArray();
            for (var i = 0; i < serializedData.length; i++) {
                data[serializedData[i].name] = serializedData[i].value;
            }

            return data;
        },
        areFieldsValid: function($fields) {
            var $fields = $($fields);

            var areAllFieldsValid = true;

            $fields.each(function() {
                var fieldClasses = this.className.split(' ');
                for (var i = 0; i < fieldClasses.length; i++) {
                    try {
                        var validator = Validation.get(fieldClasses[i]);

                        if(Validation.isVisible(this) && !validator.test($F(this), this)) {
                            areAllFieldsValid = false;
                            return false;
                        }
                    } catch(e) {
                        areAllFieldsValid = false;
                        return false;
                    }
                }
            });

            return areAllFieldsValid;
        },
        showContainerOverlay: function(container) {
            var $container = $(container);
            if (!$container.length) {
                return;
            }

            var $overlay = $container.find('> .stenik-onepage-section-overlay');
            if (!$overlay.length) {
                $container.css({position: 'relative'});
                $overlay = $('<div class="stenik-onepage-section-overlay"><span class="loaderIcon"></span></div>');
                $overlay.css({
                    background: 'rgba(255,255,255,0.5)',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: $container.outerWidth(false),
                    height: $container.outerHeight(false),
                    zIndex: 9999
                });
                $container.append($overlay);
            }

            $overlay.show().addClass('shown');
        },
        hideLoaders: function() {
            $('.stenik-checkout .stenik-onepage-section-overlay:visible').hide().removeClass('shown');
        },
        onSave: function(response){
            this.hideLoaders();
            if (response.error){
                if (typeof response.message == 'undefined' && typeof response.error_messages != 'undefined' ) {
                    response.message = response.error_messages;
                }

                if (Object.isString(response.message)) {
                    alert(response.message.stripTags().toString());
                } else {
                    var msg = response.message;
                    if(Object.isArray(msg)) {
                        msg = msg.join("\n");
                    }
                    alert(msg.stripTags().toString());
                }

                return false;
            }

            if (response.update_sections) {
                for (sectionCode in response.update_sections) {
                    var $section = jQuery('#stenik-onepage-section-' + sectionCode);
                    if ($section.length) {
                        var preservedData = {};
                        if ($section.data('section-preserve-values-on-reload') == 1) {
                            preservedData = this.serializeFields($section.find('select,input,textarea'));
                        }

                        $newSection = $(response.update_sections[sectionCode]);
                        $section.replaceWith($newSection);

                        for (dataKey in preservedData) {
                            var $field = $newSection.find('[name="' + dataKey + '"]');
                            if ($field.is('[type="checkbox"]') || $field.is('[type="radio"]')) {
                                if (preservedData[dataKey]) {
                                    $field.prop('checked', true);
                                }
                            } else {
                                $field.val(preservedData[dataKey]);
                            }


                        }

                        this.initSections($newSection);
                    }
                }
            }

            if (response.messages) {
                for (var i = 0; i < response.messages.length; i++) {
                    if (typeof response.messages[i].text != 'undefined' &&
                        typeof response.messages[i].type != 'undefined'
                    ) {
                        if (response.messages[i].type == 'error') {
                            alert(response.messages[i].text);
                        }
                    }
                }
            }

            if (response.redirect) {
                var checkout = this;
                if (checkout.currentAjax) {
                    checkout.currentAjax.always(function() {
                        checkout.showContainerOverlay($('.stenik-checkout'));
                    });
                } else {
                    checkout.showContainerOverlay($('.stenik-checkout'));
                }
                window.location = response.redirect;
                return;
            }

            if (this.nextStep) {
                this.gotoStep(this.nextStep);
                this.nextStep = false;
            }
        },
        gotoStep: function(stepCode) {
            var $step = $('#sc-checkout-step-' + stepCode);
            $step.show();
            var $stepTitle = $('#stenik-checkout-step-title-' + this.nextStep);

            $('.stenik-checkout .stenik-checkout-step-title').not($stepTitle).removeClass('done').removeClass('current');
            $stepTitle.removeClass('done').addClass('current');

            for (var i = 0; i < this.steps.length; i++) {
                if (this.steps[i] == stepCode) {
                    break;
                }
                $('#stenik-checkout-step-title-' + this.steps[i]).addClass('done');
            }

            $('.stenik-checkout .sc-checkout-step').not($step).hide();
            $step.show();
        }
    };
})(jQuery);
