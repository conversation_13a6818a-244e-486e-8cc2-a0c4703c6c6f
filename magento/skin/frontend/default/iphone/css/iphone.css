/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/* Reset
-----------------------------*/
html, body, header, footer,
h1, h2, h3, h4, h5, h6, p,
form, fieldset,
ul, li, dl, dt, dd { background:transparent; border:0; font-size:100%; margin:0; outline:0; padding:0; vertical-align:baseline; }
img { vertical-align:bottom; }
ul { list-style:none; }
a { color:#1394ca; text-decoration:none; }


/* Page
-----------------------------*/
html { -webkit-text-size-adjust:none; overflow-y:scroll; }
html, body { height:100%; }

body { background:#fff; font:15px/18px HelveticaNeue, Helvetica, sans-serif; min-height:100%; }

legend { display:none; }

.std { clear:both; margin:10px 0; }

.a-left { text-align:left; }
.a-center { text-align:center; }
.a-right { text-align:right; }

.global-site-notice { background:#444; border-bottom:1px solid #333; color:#fff; font-size:13px; padding:10px; text-shadow:0 1px 0 #111; }
.global-site-notice p { margin:0 0 10px; }
.global-site-notice a { color:#f90; text-decoration:underline; text-shadow:none; }
.global-site-notice .actions { text-align:center; }
.global-site-notice .actions button { background:none; border:1px solid #ccc; color:#fff; cursor:pointer; padding:5px 10px; text-shadow:0 1px 0 #333; }

input.input-text,
input[type="text"],
input[type="password"],
input[type="email"] {
    background:#fff;
    border:1px solid #e4e4e4;
    border-radius:2px;
    font-size:15px;
    margin:0;
    padding:5px;
    width:100%;
    -webkit-appearance:none;
    -webkit-box-sizing:border-box;
            box-sizing:border-box;
    -webkit-box-shadow:inset 1px 1px 3px rgba(0, 0, 0, .05);
       -moz-box-shadow:inset 1px 1px 3px rgba(0, 0, 0, .05);
            box-shadow:inset 1px 1px 3px rgba(0, 0, 0, .05);
}

textarea {
    border:1px solid;
    border-color:#e4e4e4;
    border-radius:2px;
    padding:5px;
    font:15px/18px HelveticaNeue, Helvetica, sans-serif;
    height:100px;
    width:100%;
    resize:vertical;
    -webkit-appearance:none;
    -webkit-box-sizing:border-box;
            box-sizing:border-box;
    -webkit-box-shadow:inset 1px 1px 3px rgba(0, 0, 0, .05);
       -moz-box-shadow:inset 1px 1px 3px rgba(0, 0, 0, .05);
            box-shadow:inset 1px 1px 3px rgba(0, 0, 0, .05);
}


select, .select-multiple {
    background:url(../images/i_dropdown.png) no-repeat right #fefefe;
    border:1px solid #e4e4e4;
    -webkit-border-radius:2px;
       -moz-border-radius:2px;
            border-radius:2px;
    color:#333;
    font:15px/18px HelveticaNeue, Helvetica, sans-serif;
    padding:5px 30px 5px 5px;
    width:100%;
    -webkit-appearance:none;
       -moz-appearance:none;
            appearance:none;
    -webkit-box-sizing:border-box;
       -moz-box-sizing:border-box;
            box-sizing:border-box;
    -webkit-box-shadow:inset 1px 1px 1px rgba(0, 0, 0, .1);
       -moz-box-shadow:inset 1px 1px 1px rgba(0, 0, 0, .1);
            box-shadow:inset 1px 1px 1px rgba(0, 0, 0, .1);
}
select:disabled { background:#f5f5f5; }
.select-multiple-wrap { position:relative; }
.select-multiple { max-width:100%; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; position:relative; }
.select-multiple-options-wrap {
    background:#fff;
    border:1px solid #e4e4e4;
    border-radius:2px;
    position:absolute;
    top:-5px;
    left:-5px;
    right:-5px;
    visibility:hidden;
    -webkit-box-shadow:0 3px 6px rgba(0, 0, 0, .25);
       -moz-box-shadow:0 3px 6px rgba(0, 0, 0, .25);
            box-shadow:0 3px 6px rgba(0, 0, 0, .25);
    z-index:1;
}
.select-multiple-options-wrap .select-heading {
    border-bottom:1px solid #e4e4e4;
    color:#424242;
    font-weight:bold;
    font-size:15px;
    line-height:20px;
    padding:10px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}
.select-multiple-options-wrap .select-close {
    cursor:pointer;
    color:#999;
    display:inline-block;
    float:right;
    font-size:18px;
    line-height:1;
    text-align:center;
    height:20px;
    width:20px;
}
.select-multiple .selected-counter {
    background:#fff;
    color:#999;
    display:inline-block;
    font-size:12px;
    font-weight:bold;
    padding:2px 5px 5px 10px;
    position:absolute;
    top:3px;
    bottom:0;
    right:25px;
}
.select-multiple-options {
    list-style:none;
    margin:0 auto;
    padding:0;
}
.select-multiple-options li {
    border-bottom:1px solid #e4e4e4;
    cursor:pointer;
    margin:0 !important;
    padding:10px 10px 10px 38px;
    position:relative;
}
.select-multiple-options li.active {
    background:#f5f5f5;
}
.select-multiple-options li:after {
    content:'';
    display:block;
    background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAKCAYAAABv7tTEAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYxIDY0LjE0MDk0OSwgMjAxMC8xMi8wNy0xMDo1NzowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNS4xIFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDAxRDg0NzcxQjdEMTFFMUE1RjVCOUQyODJGQTNDMEMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDAxRDg0NzgxQjdEMTFFMUE1RjVCOUQyODJGQTNDMEMiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpEMDFEODQ3NTFCN0QxMUUxQTVGNUI5RDI4MkZBM0MwQyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpEMDFEODQ3NjFCN0QxMUUxQTVGNUI5RDI4MkZBM0MwQyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pv02V7QAAABZSURBVHjalM5BDoAwCATAfUXj/7/Dbzx43QppCW0U5bAh0AwUJPEnItIA8K4lcCqyFMFlfRXo3B/m4AsY8n8GmIF56dhhBgyNzREyA44e4CtYUIQZ0HQBBgCyITMDEAMA1AAAAABJRU5ErkJggg==) no-repeat 0 0;
    height:10px;
    width:13px;
    position:absolute;
    top:15px;
    left:15px;
    opacity:0.2;
}
.select-multiple-options li.active:after {
    opacity:1;
}
.select-multiple-options li:last-child {
    border-bottom:none;
}
.select-multiple-options li + li {
    border-top:1px linear #fff;
}

.pager select { width:auto; }

a.btn-remove,
 .btn-remove2 { background:url(../images/btn_remove.png) no-repeat center; display:inline-block; overflow:hidden; text-indent:-100%; height:25px; width:25px; }
a.link-edit { background:url(../images/btn_edit.png) no-repeat left; display:inline-block; font-size:11px; font-weight:normal; line-height:18px; padding:0 0 0 23px; }

.reward-message,
.messages .note-msg,
.messages .error-msg,
.messages .notice-msg,
.messages .success-msg { background:#444; color:#fff; padding:10px; text-shadow:0 1px 0 #111; }

.no-display { display:none; }
.nobr { white-space:nowrap; }

.window-overlay { display:none; }

.map-popup,
.popup-block,
.remember-me-popup {
    background:#fcfcfc;
    position:absolute;
    left:15px !important;
    right:15px !important;
    margin:-18px 0 0;
    padding:15px;
    -webkit-box-shadow:0 2px 6px rgba(0, 0, 0, .25);
       -moz-box-shadow:0 2px 6px rgba(0, 0, 0, .25);
            box-shadow:0 2px 6px rgba(0, 0, 0, .25);
}
.popup-block { background:#fff; display:none; position:fixed; top:50%; left:50%; margin:-60px 0 0 -140px; width:250px; }
.popup-block .form-list { margin:0 0 15px; }
.popup-block .block-title { display:none; }
.popup-block .buttons-set button { display:inline-block !important; margin-right:1px !important; width:49% !important; }
.popup-block.active { display:block; }

.map-popup-close { float:right; }
.map-popup-heading,
.map-popup-checkout,
.map-popup-price { margin:0 0 10px; }
.map-popup-price .special-price .price-label { color:#222; }
.map-popup-price .special-price .price { font-weight:bold; }

.remember-me-popup-body { line-height:20px; }
.remember-me-popup-close {
    background:#fff;
    border:1px solid #e4e4e4;
    color:#424242;
    display:block;
    margin:15px auto 0;
    text-align:center;
    font-weight:bold;
    font-variant:small-caps;
    text-transform:lowercase;
    text-shadow:0 -1px 0 #eee;
    padding:5px 10px;
    width:100px;
    -webkit-box-shadow:0 1px 2px rgba(0, 0, 0, .1);
       -moz-box-shadow:0 1px 2px rgba(0, 0, 0, .1);
            box-shadow:0 1px 2px rgba(0, 0, 0, .1); }

#remember-me-box a { font-weight:bold; border-bottom:1px dashed; }

/* Header
-----------------------------*/
body > header {
    background:url(../images/bg_gradient.png) repeat-x 0 0; /* Theme color1 */
    box-sizing:border-box;
    display:table;
    position:relative;
    width:100%;
    height:45px;
    z-index:102;
    -webkit-user-select:none;
    -webkit-transform:translate3d(0, 0, 1px);
    -webkit-transform-style:preserve-3d;
    -webkit-tap-highlight-color:rgba(0,0,0,0);
    -webkit-touch-callout:none;
}

body > header.no-shadow {
    -webkit-box-shadow:none;
            box-shadow:none;
}

.global-site-notice { -webkit-transform:translate3d(0, 0, 1px); }

.cart-empty { padding:15px; }

.header-bg {
    background:url(../images/custom/bg_header.png) no-repeat center #fff;
    height:45px;
    -webkit-user-select:none;
    -webkit-transform:translate3d(0, 0, 1px);
    -webkit-transform-style:preserve-3d;
    -webkit-tap-highlight-color:rgba(0,0,0,0);
    -webkit-touch-callout:none;
}

.header-bg .header-logo { background:url(../images/custom/bg_logo.png) no-repeat 0 0; display:block; height:45px; position:absolute; width:100%; }

body > header .menu-wrapper {
    display:table;
    width:100%;
}

body > header .menu-wrapper {
    vertical-align:bottom;
}

body > header dl {
    display:table-cell;
    min-width:100px;
    width:100px;
    vertical-align:top;
}

body > header dt,
body > header dd {
    display:block;
}

body > header dt { background:url(../images/bg_divider.png) no-repeat right; float:left; padding:0 1px 0 0; height:45px; text-indent:-100%; width:49px; }

body > header dd {
    position:absolute;
    top:0;
    left:0;
    padding:10px;
    white-space:normal;
    margin-top:44px;
    visibility:hidden;
    -webkit-box-sizing:border-box;
    box-sizing:border-box;
    -webkit-transition:-webkit-transform 250ms ease-out;
}

@media all and (-webkit-transform-3d) {
    body > header dd { -webkit-transform:translate3d(0, -100%, -1px); }
}

body > header .search {
    display:table-cell;
    overflow: hidden;
    padding:5px 15px;
    vertical-align:middle;
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  display: none;
}

body > header .search input {
    background:url(../images/i_search.png) no-repeat 8px 6px #fff;
    border:none;
    box-sizing:border-box;
    color:#949494;
    outline:none;
    font-size:18px;
    line-height:1;
    margin:0;
    padding:4px 5px 4px 30px;
    width:100%;
    
    border-radius:15px;
    
    -webkit-appearance:none;
            appearance:none;
            
    -webkit-box-shadow:inset 0 1px 1px #555;
       -moz-box-shadow:inset 0 1px 1px #555;
            box-shadow:inset 0 1px 1px #555;
}

.search-autocomplete {
    background:#333;
}
.search-autocomplete li {
    color:#fff;
    font-size:14px;
    padding:5px;
}
.search-autocomplete li + li { border-bottom:1px solid #999; }
.search-autocomplete .amount { background:#fff; border-radius:15px; color:#111; display:inline-block; font-size:8px; margin-right:10px; padding:0 5px; vertical-align:middle; }

body > header dt a {}

body > header dt.cart-icon { position:relative; white-space:nowrap; overflow:hidden; }
body > header dt.cart-icon > a { background:url(../images/i_cart.png) no-repeat center; display:block; height:45px; }

body > header dt.menu > a { background:url(../images/i_menu.png) no-repeat center; display:block; height:45px; position:relative; }

body > header dt.cart-icon.active > a,
body > header dt.menu.active > a { background-color:rgba(0, 0, 0, .10); }

body > header dt.cart-icon > span:nth-child(2),
body > header .badge {
    background:none;
    display:inline-block;
    color:#fff;
    height:auto;
    font-size:12px;
    font-weight:bold;
    line-height:14px;
    position:absolute;
    text-indent:0;
    right:4px;
    top:4px;
    z-index:100;
    text-shadow:0 1px 0 #444;
}


body > header dt.menu .badge {
    top:1px;
    right:5px;
}

body > header dd.menu-box {
    padding:0;
    -webkit-transition:-webkit-transfrom 300ms linear;
    -webkit-box-shadow:3px 3px 3px rgba(0, 0, 0, .2);
       -moz-box-shadow:3px 3px 3px rgba(0, 0, 0, .2);
            box-shadow:3px 3px 3px rgba(0, 0, 0, .2);
}

body > header dd.menu-box a,
body > header dd.menu-box strong {
    background-color:rgba(255, 255, 255, .92) !important;
    color:#444444;
    font-size:18px;
    padding:10px;
    display:block;
}
body > header dd.menu-box .compare-clear {
    float:right;
    line-height:18px;
}
body > header dd.menu-box .badge {
    color:#fff;
    text-shadow:0 1px 0 rgba(0, 0, 0, 0.25);
    background:transparent;
    border:1px solid #ccc;
    font-weight:normal;
    font-size:10px;
    line-height:10px;
    padding:0 3px 2px;
    text-align:right;
    top:-5px;
    left:0;
    position:relative;
    -webkit-box-shadow:0 3px 3px rgba(0, 0, 0, 0.5), inset 0 3px 3px rgba(255, 255, 255, 0.35);
            box-shadow:0 3px 3px rgba(0, 0, 0, 0.5), inset 0 3px 3px rgba(255, 255, 255, 0.35);
}

body > header dd.menu-box .welcome-msg {
    border-bottom:2px solid #585858;
    font-weight:bold;
    padding:10px;
    text-shadow:0 -1px 0 #000;
    -webkit-box-shadow:inset 0 -2px 2px rgba(0, 0, 0, 0.35);
            box-shadow:inset 0 -2px 2px rgba(0, 0, 0, 0.35);
}

body > header dd.menu-box .welcome-msg a {
    color:#fb6b36;
    background:none;
    border:none;
    padding:0;
    display:inline;
}

body > header dd.menu-box ol {
    background:#fff;
    box-shadow:inset 0 0 3px #000;
    list-style:none;
    margin:5px;
    padding:5px;
    -webkit-box-shadow:inset 0 0 3px #000;
            box-shadow:inset 0 0 3px #000;
}

body > header dd.menu-box ol li a {
    background:transparent;
    border-bottom:1px solid #eee;
    color:#111;
    padding:5px 5px 5px 20px;
}

body > header dd.menu-box ol li:last-child a {
    border:none;
}

body > header dd.menu-box ol li.selected {
    font-weight:bold;
}

body > header dd.menu-box ol li.selected a {
    background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAKCAYAAABv7tTEAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYxIDY0LjE0MDk0OSwgMjAxMC8xMi8wNy0xMDo1NzowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNS4xIFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RjdEODVBMkI5Mzc4MTFFMEE2MDE5MzgwM0I5RkE1QjIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RjdEODVBMkM5Mzc4MTFFMEE2MDE5MzgwM0I5RkE1QjIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGN0Q4NUEyOTkzNzgxMUUwQTYwMTkzODAzQjlGQTVCMiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGN0Q4NUEyQTkzNzgxMUUwQTYwMTkzODAzQjlGQTVCMiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PnMJ/AIAAABLSURBVHjaYmAgHgQAsQAJ6hkSgPg/EJ8nViNMAwwHwCQUiNSQgC6RQKwGEHiPRQKvBhAwQNM4n5AGXBoJasClMYHY+IBpxKsBIMAAi14oP6lEWTAAAAAASUVORK5CYII=) no-repeat left;
}

/* Footer
-----------------------------*/
body > footer { min-height:100px; }
body > address.copyright { color:#fff; line-height:18px; padding:15px; }
body > address.copyright .logo { float:left; margin:0 10px 0 0; }
body > footer ul { line-height:27px; padding:10px 15px 0; overflow:hidden; }
body > footer ul + ul { padding:0 15px 10px; }
body > footer ul li { box-sizing:border-box; float:left; width:50%; }
body > footer ul li a { color:#fff; display:inline-block; padding:2px; }

.block-subscribe { padding:10px 15px; }
.block-subscribe .block-title { display:none; }
.block-subscribe .form-subscribe-header { margin:0 0 5px; padding-left:12px; }
.block-subscribe .input-box { display:inline-block; vertical-align:top; width:60%; }
.block-subscribe .input-box input { border-radius:15px; border-color:#ccc; font-size:18px; padding:5px 10px; }
.block-subscribe .validation-advice { padding:10px 0 0 12px; width:150%; }
.block-subscribe .actions { display:inline-block; vertical-align:top; width:38%; }
.block-subscribe button {
    border:none;
    -webkit-border-radius:20px;
       -moz-border-radius:20px;
            border-radius:20px;
    color:#fff;
    font-size:18px;
    padding:5px 10px;
    vertical-align:top;
    
    -webkit-appearance:none;
            appearance:none;
}

/* Content
-----------------------------*/
body > section:before, body > section:after { content: ""; display: table; }
body > section:after { clear:both; }
body > section img { max-width:100%; }
body > section > .category-image { margin:0 -10px; }
body > section > .category-image img { min-width:100%; }
body > section > .std,
body > section > form > .fieldset { margin:0; padding:15px; }
body > section > form > .buttons-set { padding:15px; }
body > section > form > .buttons-set p.required { font-size:10px; line-height:12px; margin:10px 0; text-align:right; }
body > section > form > .required { padding:15px; }
body > section > form > .fieldset h2 { color:#424242; font-size:18px; line-height:22px; margin:0 0 15px; }

.front-banner {
    text-align:center;
}

.page-sitemap { padding:15px; }
.page-sitemap .page-title { display:none; }

/* Categories
-----------------------------*/
.nav-wrap {
    overflow:hidden;
}
#nav-container {
    overflow:hidden;
    padding:0 0 1px;
    position:relative;
    width:9000px;
    -webkit-transform:translate3d(0, 0, 0);
   -webkit-transition:-webkit-transform 250ms linear;
        -o-transition:-o-transform 250ms linear;
           transition:transform 250ms linear;
}
#nav-container ul > li > ul { display:none; }
#nav-container:after { content: "."; display: block; clear: both; visibility: hidden; line-height: 0; height: 0; }
#nav-container ul { font-size:21px; margin:0; width:100%; float:left; }
#nav-container li.subcategory-header { background:url(../images/bg_gradient.png) repeat-x 0 0 #d9d9d9; height:28px; padding:6px 10px; text-align:center; font-weight:bold; }
#nav-container li.subcategory-header span { color:#414141; line-height:28px; }
#nav-container li.subcategory-header .button-wrap { float:left; position:absolute; }
#nav-container li.subcategory-header .button-wrap button {
    background:url(../images/bg_back_btn.png) no-repeat right top;
    border:none;
    height:27px;
    width:47px;
    color:#fff;
    display:inline-block;
    font-size:11px;
    font-weight:bold;
    line-height:27px;
    margin:0;
    padding:0 0 0 7px;
    position:relative;
    z-index:3;
    -webkit-appearance:none;
            appearance:none;
}
#nav-container li a {
    background-color:#fff !important;
    display:block;
}
#nav-container li a span {
    background:url(../images/custom/arrow.png) no-repeat right;
    display:block;
    line-height:20px;
    padding:10px 15px; 
    -webkit-background-origin:content-box;
            background-origin:content-box;
}


/* Form-list
-----------------------------*/
.form-list li { margin:0 0 15px; }
.form-list li:last-child { margin:0; }
.form-list label { color:#424242; display:block; font-weight:bold; margin:0 0 2px; }
.form-list label em { color:#f9721f; margin-right:2px; }
.form-list .field { margin:0 0 15px; }
.form-list .field:last-child { margin:0; }
.form-list .add-row .btn-remove { float:right; }
.form-list .add-row .btn-remove a { vertical-align:top; }
.form-list .control .input-box { vertical-align:top; }
.form-list .control .input-box,
.form-list .control label { display:inline-block; }
.form-list .control label { max-width:90%; }
.form-list .validation-advice { font-size:14px; padding:2px 0; }
.form-list img[src*="calendar.gif"] { position:absolute; padding:7px; }
.form-list input.datetime-picker { padding-left:30px; }
.form-list input[type="radio"] + label { display:inline-block; }
.form-list input[type="text"]::-webkit-input-placeholder,
.form-list input[type="password"]::-webkit-input-placeholder,
.form-list input[type="email"]::-webkit-input-placeholder { color:#444; }
/*--------------------------*/
input[type="checkbox"] {
    background:url(../images/bg_checkbox.png) no-repeat 0 0;
    border:1px solid #e4e4e4;
    border-radius:2px;
    margin:0 3px 0 0;
    height:20px;
    width:20px;
    vertical-align:top;
    -webkit-appearance:none;
}
input[type="checkbox"]:checked {
    background-position:0 -19px;
}

/* Account
-----------------------------*/
.account-login .col-1,
.account-login .col-2,
.account-create .fieldset,
.customer-account-forgotpassword .fieldset,
.customer-account-logoutsuccess .page-title + p { padding:15px; }
.account-login h2,
.account-login .legend,
.account-create .legend { color:#424242; font-size:18px; font-weight:bold; margin:0 0 15px; }
.account-login .legend + p { display:none; }
.account-login p.required,
.account-create p.required,
.customer-account-forgotpassword p.required { font-size:10px; line-height:12px; margin:10px 0 0; text-align:right; }
.account-create p.required,
.customer-account-forgotpassword p.required { padding:0 15px 15px; }

.account-create .buttons-set,
.customer-account-forgotpassword .buttons-set { padding:0 15px 15px; }
.account-create .buttons-set .back-link,
.customer-account-forgotpassword .buttons-set .back-link { display:none; }

.customer-account-forgotpassword .fieldset p { margin:0 0 10px; }

/* Captcha
-----------------------------*/
.captcha-image { position:relative; }
.captcha-image .captcha-img { border:1px solid #e1e1e1; border-radius:5px; -moz-border-radius:5px; -webkit-border-radius:5px; }
.captcha-reload { position:absolute; top:5px; left:0; padding:10px; }

/* Breadcrumbs
-----------------------------*/
.breadcrumbs { display:none; }
.product-tags-list .page-title h1,
.catalogsearch-result-index .page-title h1 { padding-right:55px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; }
.catalogsearch-result-index .page-title h1 { padding-right:85px; }

.page-title {
    background-color:#fff !important;
    color:#424242;
    height:25px;
    margin:0;
    padding:10px 15px;
}

.page-title h1 {
    font-size:18px;
    line-height:25px;
    text-align:left;
    overflow:hidden;
    white-space:nowrap;
    text-overflow:ellipsis;
}

/* Catalog List
-----------------------------*/
.category-description { display:none; margin:0; padding:10px; }

.more { padding:15px 10px; text-align:center; }
.more-button { background:url(../images/bg_gradient.png) repeat-x #1394ca center; color:#fff; font-size:21px; line-height:25px; display:inline-block; padding:5px 25px; position:relative; vertical-align:top; }
.more-button.loading:before { background:url(../images/loader.gif) no-repeat center; background-size:24px 24px; content:''; display:inline-block; height:24px; width:24px; left:-30px; position:absolute; }

.filters-block ol,
.filters-block li,
.filters-block dl,
.filters-block dt,
.filters-block dd {
    margin:0;
    padding:0;
}

.filters-block-wrap {}

.filters-block { color:#424242; margin:0; padding:10px 10px 15px; }
.filters-block dl { display:inline-block; vertical-align:top; width:49%; }

@media(orientation:landscape) {
    .filters-block dl { width:33%; }
}

.filters-block dt { color:#424242; font-weight:bold; margin:8px 0 0; }
.filters-block h3 { color:#424242; font-size:18px; line-height:20px; }
.filters-block .currently { color:#424242; }
.filters-block .actions { margin:0 0 18px; }
.filters-block .currently .block-subtitle { font-weight:bold; font-size:18px; line-height:20px; margin:5px 0 8px; }
.filters-block .currently .btn-remove { height:20px; width:20px; vertical-align:top; }
.filters-block .currently li { line-height:20px; }
.filters-block dd {}
.filters-block ol { list-style:none; line-height:17px; width:99%; }
.filters-block li { padding:9px 0; }
.filters-block a { color:#1394ca; font-weight:bold; }

.toolbar {
    font-size:12px;
    padding:7px 5px 3px;
    height:35px;
    overflow:hidden;
    text-align:right;
    position:relative;
    top:-45px;
    margin:0 0 -45px;
}
.toolbar .pager,
.toolbar .pager .amount,
.toolbar .pager .pages,
.toolbar .limiter {
    float:right;
}
.toolbar .amount,
.toolbar .view-mode {
    line-height:23px;
    padding:0 4px;
}
.toolbar .view-mode label {
    font-weight:bold;
    position:relative;
    top:1px;
    vertical-align:middle;
}
.toolbar .pager { display:none; }
.toolbar .pager .pages strong {
    margin-right:5px;
    vertical-align:middle;
}
.toolbar .pager .pages select
.toolbar .limiter select {
    background:-webkit-gradient(linear, 0 0, 0 100%, from(#fff), to(#dedede));
    background:-o-linear-gradient(top, #fff 0%, #dedede 100%);
    border:1px solid #bbb;
    border-radius:4px;
    color:#707070;
    margin:0;
    padding:5px 10px;
    font-size:12px;
    line-height:17px;
    vertical-align:middle;
    -webkit-box-shadow:0 0 5px rgba(0, 0, 0, 0.15);
            box-shadow:0 0 5px rgba(0, 0, 0, 0.15);
    -webkit-background-clip:padding-box;
            background-clip:padding-box;
}
.toolbar .limiter select {
    vertical-align:baseline;
}
.toolbar .sort-by-wrap {
    position:absolute;
    top:7px;
    right:5px;
}
.toolbar .sort-by select {
    background:none;
    border:none;
    margin:0;
    padding:4px 5px 5px;
    opacity:0;
    width:50px;
    max-width:50px;
    vertical-align:middle;
}
.toolbar .sort-by > label {
    background:url(../images/bg_gradient.png) repeat-x 0 0 #4c4c4c;
    background-size:1px 30px;
    color:#fff;
    display:inline-block;
    font-size:14px;
    font-weight:bold;
    line-height:30px;
    padding:0 11px;
    text-align:center;
    vertical-align:middle;
    width:28px;
    -webkit-border-radius:5px;
       -moz-border-radius:5px;
            border-radius:5px;
    -webkit-box-shadow:0 0 5px rgba(0, 0, 0, 0.15);
            box-shadow:0 0 5px rgba(0, 0, 0, 0.15);
}

.toolbar .order {
    background:url(../images/bg_gradient.png) repeat-x 0 0 #4c4c4c;
    background-size:1px 30px;
    display:inline-block;
    vertical-align:top;
    border-radius:5px;
    vertical-align:top;
    text-align:center;
    margin:0;
    position:relative;
    line-height:30px;
    height:30px;
    width:30px;
    text-indent:-999em;
    -webkit-box-shadow:0 0 5px rgba(0, 0, 0, 0.15);
            box-shadow:0 0 5px rgba(0, 0, 0, 0.15);
}
.toolbar .order:after {
    content:'';
    position:absolute;
    top:10px;
    z-index:2;
    font-size:0;
    line-height:0;
    width:0;
}
.toolbar .order.asc:after {
    border-left:5px solid transparent;
    border-right:5px solid transparent;
    border-bottom:10px solid #fff;
    right:10px;
}
.toolbar .order.desc:after {
    border-left:5px solid transparent;
    border-right:5px solid transparent;
    border-top:10px solid #fff;
    right:10px;
}
.toolbar a {
    color:#707070;
}

.c-list > li {
    background-color:#fff !important;
    color:#424242;
    position:relative;
    border-bottom:1px solid #ebe9eb;
}
.c-list > li:last-child { display:block; border-bottom:none; }
.c-list > li > a {
    background:url(../images/custom/arrow.png) no-repeat right; 
    -webkit-background-origin:content-box;
    background-origin:content-box;
    color:#2f2f2f;
    display:block;
    padding:10px;
    -webkit-user-select:none;
    user-select:none;
    -webkit-touch-callout:none;
            touch-callout:none;
    -webkit-tap-highlight-color:rgba(0,0,0,0);
}
.c-list > li > a:after {
    content:".";
    display:block;
    clear:both;
    visibility:hidden;
    line-height:0;
    height:0;
}

.c-list h1 { font-size:16px; line-height:20px; font-weight:bold; }
.c-list .cloned-wrap { position:absolute; padding:10px; opacity:0; }
.c-list .cloned-wrap .product-image img { -webkit-box-shadow:none; box-shadow:none; }
.c-list .product-image { float:left; margin-right:10px; -webkit-transform:translate3d(0,0,0); }
.c-list .product-shop { overflow:hidden; padding:0 30px 0 0; }
.c-list .product-shop h1 { font-weight:bold; text-align:left; margin:0 0 7px; }
.c-list .product-shop .price-box { margin:0 0 5px; }

.to-cart-animate {
    -webkit-animation:bounce-rotate 200ms infinite
}

.drop-start { -webkit-transform:scale(1.2); opacity:0.9; }

@-webkit-keyframes bounce-rotate {
  0%   { -webkit-transform:rotate(3deg) skew(1deg) scale(1.2); }
  25%  { -webkit-transform:rotate(0) skew(0) scale(1.2); }
  50%  { -webkit-transform:rotate(-3deg) skew(-1deg) scale(1.2); }
  75%  { -webkit-transform:rotate(0) skew(0) scale(1.2); }
  100% { -webkit-transform:rotate(3deg) skew(1deg) scale(1.2); }
}

/* Product Info Box
-----------------------------*/

.price-box .price-including-tax,
.price-box .price-excluding-tax { display:block; }
.price-box .price-including-tax .price { font-weight:bold; }

.product-view {}
.product-view .product-name h1 { color:#424242; font-size:18px; font-weight:bold; text-align:left; line-height:20px; margin:0 0 9px; }
.product-view .product-image-wrap { padding:10px; position:relative; }
.product-view .product-image { position:relative;; }
.product-view .product-image li { text-align:center; }
.product-view .product-image li a { display:block; }

.product-view .product-image-wrap img { position:relative; }
.product-view .product-image-wrap img.cloned { position:absolute; top:10px; left:50%; margin:0 0 0 -72px; -webkit-transition:all ease-in 400ms; -moz-transition:all ease-in 400ms; -o-transition:all ease-in 400ms; transition:all ease-in 400ms; }
.product-view .product-image-wrap img.animate { z-index:101; }

.product-view .product-shop { padding:15px; }
.product-view .product-shop .buttons-set { margin:10px 0 0; }
.product-view .product-shop .product-main-info .price-box { color:#424242; font-size:18px; line-height:26px; margin:10px 0; }
.product-view .product-shop .product-main-info .price-box-bundle .price-box {}
.product-view .product-shop .product-main-info .item-options { margin:10px 0 0; }
.product-view .product-shop .product-main-info .item-options dt { color:#424242; font-style:15px; font-weight:bold; }
.product-view .product-shop .product-main-info + .options-container-small { margin:15px 0 0; }

.product-view .product-shop .availability { color:#424242; font-size:13px; font-weight:bold; margin:0 0 9px; }
.product-view .product-shop .availability span { font-weight:normal; }
.product-view .product-shop .tier-prices { margin:10px 0; }

.product-view .product-shop .add-to-box {}
.product-view .product-shop .add-to-box .qty,
.product-view .product-shop .add-to-box label { display:none; }
.product-view .price-box .price-label { vertical-align:baseline; }
.product-view .price-box .old-price { color:#ccc; }
.product-view .price-box .special-price .price-label { color:#222; }
.product-view .price-box .special-price .price { font-weight:bold; }

.product-view .add-to-cart label[for="qty"],
.product-view .add-to-cart input.qty { display:none; }

.product-view .product-img-box .product-image:before { background:url(../images/i_zoom.png) no-repeat 0 0; content:''; display:block; position:absolute; top:15px; right:25px; height:14px; width:14px; }
.product-view .product-img-box .controls .prev,
.product-view .product-img-box .controls .next { height:auto; top:40px; bottom:40px; width:auto; z-index:101; }
.product-view .product-img-box .controls .prev { left:0; right:80%; }
.product-view .product-img-box .controls .next { left:80%; right:0; }

.product-view .short-description { margin:0; padding:15px; }

.product-view .product-add-to { padding:0 15px 15px; }

.send-friend .fieldset,
.send-friend .buttons-set { padding:15px; }
.send-friend .buttons-set .back-link { display:none; }
.send-friend .fieldset .btn-remove { float:right; position:relative; top:-4px; right:-4px; }
.send-friend .fieldset .legend { color:#424242; font-size:18px; margin:0 0 10px; }
.send-friend #max_recipient_message,
.send-friend #add_recipient_button { margin:10px 0 0; }

.product-collateral { clear:both; }
.product-collateral .box-collateral { padding:15px; }

.product-collateral .box-additional { background:none; padding:0; }
.product-collateral .box-additional h2 { display:none; }
.product-collateral .box-additional .data-table { color:#424242; border-collapse:collapse; width:100%; }
.product-collateral .box-additional .data-table th,
.product-collateral .box-additional .data-table td { padding:15px; text-align:left !important; vertical-align:top; }
.product-collateral .box-additional .data-table th { font-weight:bold; }

.rating-box { background:url(../images/i_star_blank.png) repeat-x center left; height:18px; width:100px; }
.rating-box .rating { height:18px; background:url(../images/i_star.png) repeat-x center left; }

.product-view .rating-box { margin:0 0 10px; }
.product-view .rating-links { color:#666; text-align:right; }
.product-view .rating-links a { color:#1394ca; display:inline-block; text-decoration:underline; }
.product-view .rating-links a:first-child { float:left; margin-right:10px; }
.product-view .rating-links .separator { display:none; }

.product-view .box-description .std { margin:0; }

.product-view .product-shop .add-to-box button,
.product-view .product-options-bottom .add-to-cart button { background:url(../images/bg_gradient.png) repeat-x 0 0 #1394ca; color:#fff; font-size:24px; display:block; border:0; height:45px; line-height:45px; margin:15px 0 0; padding:0; -webkit-appearance:none; appearance:none; text-shadow:0 -1px 0 rgba(0, 0, 0, .45); width:100%; }
.product-view .product-options-bottom .add-to-cart + .add-to-links { margin:10px 0 0; }
.product-view .product-options-bottom .add-to-cart + .add-to-links li:first-child a { width:100%; }

.paypal-logo { text-align:center; }
.paypal-or { display:block; padding:10px 0; text-align:center; }

/* Carousel */

.carousel-wrap {}
.carousel-wrap li { display:inline-block; vertical-align:top; white-space:normal; }
.carousel-wrap .box-title { max-width:80%; }
.carousel-wrap .ratings { display:none; }
.carousel-wrap .carousel-items { white-space:nowrap; -webkit-transition:all 150ms linear; -moz-transition:all 250ms linear; -o-transition:all 250ms linear; transition:all 250ms linear; }
.carousel-wrap .carousel-items-wrap { overflow:hidden; }

@media all and (-webkit-transform-3d) {
.carousel-wrap .carousel-items { -webkit-transform:translateX(0); -webkit-perspective:1000; -webkit-backface-visibility:hidden; }
}

.box-up-sell { position:relative; padding:15px; }
.box-up-sell h2 { color:#424242; font-size:18px; font-weight:bold; line-height:25px; margin:0 0 10px; }
.box-up-sell .item { color:#424242; font-size:15px; line-height:18px; }
.box-up-sell .item a { color:#424242; }
.box-up-sell .item h3 { max-height:36px; margin:0 0 5px; overflow:hidden; text-overflow:ellipsis; width:100%; }
.box-up-sell .item img { margin:0 0 5px; }
.box-up-sell .price-box .price { font-weight:bold; }
.box-up-sell .product-image {}
.box-up-sell .controls { background:url(../images/bg_divider_dark.png) no-repeat center; position:absolute; top:15px; right:15px; height:24px; width:49px; }

.controls .prev,
.controls .next { display:block; position:absolute; top:0; right:0; height:24px; width:24px; }
.controls .prev { background:url(../images/arrow_left.png) no-repeat center; }
.controls .next { background:url(../images/arrow_right.png) no-repeat center; }
.controls .prev.disabled,
.controls .next.disabled { opacity:.25; }
.controls .prev { right:25px; }

.counter { padding:5px 0 0; text-align:center; }
.counter span { margin:0 2px; }
.counter span:before { content:'\2022'; color:#ddd; font-size:20px; display:inline-block; -webkit-text-stroke:1px #ddd; }
.counter span.active:before { color:#fff; }

.product-shop .grouped-items-table { border:none; border-spacing:0; font-size:15px; margin:0; -webkit-border-radius:0; width:100%; }
.product-shop .grouped-items-table th,
.product-shop .grouped-items-table td { padding:5px 0; }
.product-shop .grouped-items-table th { display:none; }
.product-shop .grouped-items-table td { background:none; border:none; border-bottom:1px solid #e4e4e4; text-align:left; vertical-align:middle; }
.product-shop .grouped-items-table tr:last-child  td { border-bottom:none; }
.product-shop .grouped-items-table input.qty { text-align:center; width:40px; }

/* ------------ */

.product-view .box-tags { padding:15px; }
.product-view .box-tags a { color:#1394ca; display:inline-block; font-weight:bold; }
.product-view .box-tags li { display:inline; margin-right:10px; }
.product-view .box-tags h2 { color:#424242; font-size:18px; font-weight:bold; margin:0 0 10px; }
.product-view .box-tags h3,
.product-view .form-add-tags { display:none; }

/* ------------ */

.add-to-links { display:table; font-size:12px; margin:0; position:relative; width:100%; text-align:center; }
.add-to-links > li { display:table-cell; text-align:center; line-height:1; vertical-align:top; }
.add-to-links > li > a,
.add-to-links > li .split-button strong { background:url(../images/bg_gradient.png) repeat-x 0 0 #1394ca; background-size:auto 30px; color:#fff; display:inline-block; font-weight:normal; height:30px; line-height:30px; text-shadow:0 -1px 0 rgba(0, 0, 0, .45); width:99.5%; }
.add-to-links > li .split-button strong + a { display:block; height:30px; margin:0 0 -30px; position:relative; top:-30px; overflow:hidden; text-indent:-200%; }
.add-to-links > li .split-button.active .list-container { display:block; }
.add-to-links > li .split-button .list-container { border:1px solid #e4e4e4; display:none; position:absolute; top:0; left:0; text-align:left; }
.add-to-links > li .split-button .list-container  li { background:#fff; border-bottom:1px solid #e4e4e4; height:30px; line-height:30px; padding:0 10px; box-shadow:0 0 3px rgba(0, 0, 0, .15); }
.add-to-links > li .split-button .list-container  li:last-child { border:none; }
.add-to-links > li .split-button .list-container .new { background:#e4e4e4; color:#424242; text-shadow:0 1px 0 #fff; }
.add-to-links > li .split-button .list-container .new:before { content:'+'; display:inline-block; margin-right:5px; }
.add-to-links .separator { display:none; }

.product-view .options-container-big   .product-options { padding:15px; }
.product-view .options-container-big   .product-options-bottom { padding:0 15px 15px; }
.product-view .options-container-small .product-options {}
.product-view .options-container-small .product-options-bottom .price-box,
.product-view .options-container-small .product-options-bottom .price-box { display:none; }

.product-view .product-options dt { margin:0 0 10px; }
.product-view .product-options dt label { color:#424242; font-size:15px; font-weight:bold; }
.product-view .product-options dt label em { color:#f9721f; margin-right:5px; }
.product-view .product-options dd { font-size:14px; border-bottom:1px solid #ccc; padding:0 0 15px; }
.product-view .product-options dd + dt { padding:15px 0 0; }
.product-view .product-options dd:last-child { border:none; padding:0; }
.product-view .product-options dd:last-child .input-box { margin:0; }
.product-view .product-options select { width:100%; }
.product-view .product-options input.qty { width:20%; }
.product-view .product-options input.qty:disabled { background:#f5f5f5; }
.product-view .product-options .input-box { margin:0 0 10px; }
.product-view .product-options .qty-holder { display:block; margin:5px 0 0; }
.product-view .product-options .qty-holder label,
.product-view .product-options .qty-holder label + input { vertical-align:middle; }
.product-view .product-options .options-list {}
.product-view .product-options .options-list li { margin:0 0 8px; }
.product-view .product-options .options-list li:last-child { margin:0; }
.product-view .product-options .options-list input,
.product-view .product-options .options-list input +  label,
.product-view .product-options .options-list input + .label { vertical-align:middle; }
.product-view .product-options .options-list input { float:left; margin-right:10px; }
.product-view .product-options .options-list input +  label,
.product-view .product-options .options-list input + .label { display:table; }
.product-view .product-options .options-list a { text-decoration:underline; }

.giftcard-send-form { padding:15px 0 0; }
.giftcard-send-form em { color:#f9721f; }
.giftcard-send-form label  { color:#424242; display:block; font-size:15px; font-weight:bold; }
.giftcard-send-form .gift-card-amount-field { padding:0; }

.giftcard-amount-form .notice { padding:5px 0 10px; text-align:right; }
.giftcard-amount-form .notice span { float:left; font-size:12px; display:block; }
.giftcard-amount-form .notice span:last-child { float:none; }

.product-view .product-options-bottom .required { font-size:10px; line-height:12px; margin:10px 0 0; text-align:right; }
.product-view .product-options-bottom .price-box { color:#424242; font-size:18px; line-height:24px; }

.catalog-product-gallery .buttons-set { padding:10px; }
.catalog-product-gallery .buttons-set a { text-align:center; }
.catalog-product-gallery .product-gallery { background-color:#fff !important; padding:10px 0 0; position:relative; overflow:hidden; height:300px; width:100%; }
.catalog-product-gallery .product-gallery .prev,
.catalog-product-gallery .product-gallery .next { background-color:#fff; background-position:center; border:1px solid #e4e4e4; border-radius:100%; box-shadow:0 0 3px rgba(0, 0, 0, .15); top:50%; z-index:101; }
.catalog-product-gallery .product-gallery .prev { right:80%; }
.catalog-product-gallery .product-gallery .next { left:80%; }
.catalog-product-gallery .product-gallery ul { display:table; width:200%; }
.catalog-product-gallery .product-gallery li { display:table-cell; padding:0; text-align:center; overflow:hidden; max-width:100%; }
.catalog-product-gallery .product-gallery li img { max-width:100%; height:auto; vertical-align:bottom; }

.catalog-product-gallery .add-to-cart {}

/* Catalog Search */

.catalogsearch-result-index .note-msg { padding:15px; }
        
/* Product review
-----------------------------*/

.product-review { padding:15px; }
.product-review .product-img-box { text-align:center; }

.box-reviews .box-title,
.box-reviews .box-content .product-name { display:none; }
.box-reviews .box-content { padding:15px; }
.box-reviews .form-list { margin:0 0 10px; }
.box-reviews  p.required { font-size:10px; line-height:12px; margin:10px 0 0; text-align:right; }
.box-reviews .form-add h4 { display:none; }

#product-reviews-list { display:none; }
#customer-reviews:target #product-reviews-list { display:block; }

.ratings-table { margin:0 0 10px; }
.ratings-table th,
.ratings-table td.label { color:#424242; font-weight:bold; text-align:left; }
.ratings-table th,
.ratings-table td { padding:3px 15px 5px 0; }
.ratings-table td { vertical-align:middle; }

#product-review-table { margin:10px 0; }
#product-review-table thead th { display:none; }
#product-review-table tbody th { color:#424242; text-align:left; padding:0 5px 5px 0; }
#product-review-table tbody td input { background:url(../images/i_star.png) no-repeat center; border:none; display:inline-block; height:18px; width:20px; opacity:0.25; margin:0; -webkit-appearance:none; -webkit-transition:all 100ms ease-in-out; }
#product-review-table tbody td input:checked,
#product-review-table tbody td.checked input { opacity:1; }

.review-product-list .breadcrumbs .product { display:none; }
.review-product-list label { display:inline-block; font-weight:bold; padding:0 0 5px; }
.review-product-list label em { margin:0 5px 0 0; color: #F4641E; }
.review-product-list .pager { display:none; }

.product-view .box-reviews { padding:15px; }
.product-view .box-reviews h2 { color:#424242; font-size:18px; margin:0 0 15px; }
.product-view .box-reviews h3 { margin:10px 0; }
.product-view .box-reviews dl { margin:0 0 15px; }
.product-view .box-reviews dt { color:#666; font-size:12px; margin:0 0 10px; }
.product-view .box-reviews dt h3 { color:#424242; display:inline; font-size:15px; }
.product-view .box-reviews dd { margin:0 0 20px; }
.product-view .box-reviews dd:last-child { margin:0; }
.product-view .box-reviews dd .date { color:#666; font-size:11px; display:block; }
.product-view .box-reviews dd + dt { border-top:1px solid #e4e4e4; padding:20px 0 0; }

.review-product-page .product-review,
.review-product-page .product-essential { display:none; }

/* User account
-----------------------------*/
.my-account {}
.my-account .breadcrumbs {}

.account-links a,
.account-links strong { padding-left:30px !important; }

.my-account .page-title {}
.my-account .page-title.title-buttons { height:auto; }
.my-account .page-title.title-buttons h1 { display:inline; white-space:normal; }
.my-account .page-title.title-buttons button {
    background:none;
    border:none;
    color:#424242;
    display:inline;
    float:right;
    font-size:15px;
    font-weight:normal;
    margin:0;
    padding:5px;
    text-decoration:underline;
}
.my-account .box-head,
.my-account .sub-title,
.my-account .table-caption { color:#424242; font-size:18px; line-height:22px; margin:0 0 15px; }
.my-account .welcome-msg { margin:-10px 0 0; padding:0 15px 15px; }
.my-account .welcome-msg p { display:none; }
.my-account .welcome-msg .sub-title { font-weight:normal; margin:0; }
.my-account .messages + .welcome-msg { margin:15px 0 0; }

.cart-collaterals,
.my-account > p,
.my-account > .col2-set,
.my-account > .order-info,
.my-account .fieldset,
.my-account .my-rewards,
.my-account .box-account,
.my-account .storecredit,
.my-account .order-history,
.my-account .order-details,
.my-account .review-history,
.my-account .giftregistry > p,
.my-account .recent-orders > p,
.my-account .review-history > p,
.my-account .recurring-profiles,
.my-account .billing-agreements,
.my-account .dashboard .reviews,
.my-account .order-page .order-info,
.my-account .order-page .order-date,
.my-account .product-review .product-details,
.my-account .dashboard .recent-orders,
.my-account .review-history .list-item,
.my-account .product-review .product-img-box,
.my-account .downloadable-products-history > p,
.my-account .downloadable-products-history .list-item { padding:15px; }

.my-account .box,
.my-account .info-box { margin:0 0 10px; }
.my-account .box .box-title,
.my-account .info-box .box-title,
.my-account .order-history .pager,
.my-account .review-history .pager { background:url(../images/bg_gradient.png) repeat-x center #7f7f7f; color:#fff; height:30px; line-height:30px; padding:5px 8px; }
.my-account .box .box-title a,
.my-account .info-box .box-title a,
.my-account .recent-orders table a,
.my-account .order-history table a {
    background:#3f3e3f;
    -webkit-border-radius:5px;
       -moz-border-radius:5px;
            border-radius:5px;
    color:#fff;
    display:inline-block;
    float:right;
    font-size:11px;
    font-weight:bold;
    line-height:11px;
    padding:9px 15px;
}
.my-account .box .box-title h3 { display:inline; }

.my-account .info-table th,
.my-account .info-table td { text-align:left; padding:2px; vertical-align:top; }
.my-account .box-content table { border-spacing:0; margin:10px 0; }
.my-account .box-content table th,
.my-account .box-content table td { padding:2px 5px 2px 0; text-align:left; vertical-align:top; }

.my-account .order-history .pager,
.my-account .review-history .pager { height:auto; line-height:34px; text-align:right; }
.my-account .order-history .pager .amount,
.my-account .review-history .pager .amount { float:left; }
.my-account .info-box .box-title .separator { display:none; }
.my-account .box .box-content,
.my-account .info-box .box-content { padding:10px 5px; }
.my-account .info-box .box-content a {}

.my-account .fieldset > .fieldset { background:none; padding:0; }
.my-account .fieldset > .fieldset + .fieldset { margin:15px 0; }
.my-account .fieldset > .fieldset > .buttons-set { padding:15px 0; }
.my-account .fieldset h2,
.my-account .fieldset h2.legend { color:#424242; font-size:18px; line-height:22px; margin:0 0 15px; }
.my-account form > .buttons-set { padding:15px; }
.my-account form > .buttons-set button + button { margin:10px 0 0 !important; }
.my-account .back-link { display:none; }
.my-account p.required { font-size:10px; line-height:12px; padding:0 15px 15px; text-align:right; }
.my-account > .buttons-set { padding:15px; }

.my-account .dashboard > .sub-title { padding:15px 15px 0; margin:0; }
.my-account .dashboard > .sub-title + .col2-set { background:none; }
.my-account .dashboard .box .box-title:before,
.my-account .dashboard .info-box .box-title:before {
    background:rgba(0, 0, 0, .5);
    border-radius:5px;
    content:'+';
    font-family:Arial;
    display:inline-block;
    line-height:15px;
    padding:5px 0;
    text-align:center;
    margin:2px 5px 0 0;
    vertical-align:top;
    width:20px;
}
.my-account .dashboard .box .box-title.collapsed:before,
.my-account .dashboard .info-box .box-title.collapsed:before { content:'−'; }
.my-account .dashboard .box-reviews ol { list-style:none; padding:0; }
.my-account .dashboard .box-reviews li + li { margin:10px 0 0; }
.my-account .dashboard .box-reviews .number { float:left; margin:0 10px 0 0; }
.my-account .dashboard .box-reviews .details { display:table; width:90%; }
.my-account .dashboard .box-reviews .details p { display:inline-block; vertical-align:middle; }
.my-account .dashboard .box-reviews .details .rating-box { display:inline-block; vertical-align:middle; }

.my-account .dashboard .recent-orders .box-content { padding:0; }

.my-account .order-details .data-table + .order-additional { margin:15px 0 0; }

/* My Orders */
.my-account .recent-orders .pager,
.my-account .recent-orders + .buttons-set { display:none; }

/* My Downloadable Products, My Reviews */
.my-account .review-history { padding:0; }

.my-account .downloadable-products-history .list,
.my-account .review-history .list { color:#424242; font-size:12px; list-style:none; margin:0; padding:0; }
.my-account .downloadable-products-history .list-item,
.my-account .review-history .list-item { margin:0; }
.my-account .downloadable-products-history .list-item dt,
.my-account .review-history .list-item dt { font-size:15px; line-height:18px; font-weight:bold; margin:0 0 10px; }
.my-account .downloadable-products-history .pager,
.my-account .review-history .pager { display:none; }

.my-account .review-history .list-item h2 { display:inline; }
.my-account .review-history .list-item dd { padding:5px 0; }
.my-account .review-history .list-item .date { font-size:12px; }

.my-account .review-history table { border-collapse:collapse; }
.my-account .review-history table td { padding:4px; vertical-align:top; }
.my-account .review-history table td .rating-box { margin:0 0 10px; }

.my-account .recent-orders table,
.my-account .order-history table { border-collapse:collapse; color:#4c4c4c; font-size:12px; width:100%; }
.my-account .recent-orders th,
.my-account .order-history table th,
.my-account .recent-orders td,
.my-account .order-history table td { padding:4px; text-align:left; vertical-align:middle; }
.my-account .recent-orders th,
.my-account .order-history table th { font-weight:bold; padding:11px 8px; }
.my-account .recent-orders td a,
.my-account .order-history td a { font-size:12px; float:none; padding:6px 8px; }

/* My Billing Agreements */
.billing-agreement-view .my-account .page-title { height:auto; }
.billing-agreement-view .my-account .page-title h1 { display:block; margin:0 0 10px; white-space:normal; }
.billing-agreement-view .my-account .page-title h1 span { display:block; }

.my-account .billing-agreements > p { padding:15px; }
.my-account .billing-agreements .box-content > p + .form-list { margin:10px 0 0; }
.my-account .billing-agreements .box-content .form-list select { margin:0 0 15px; }

.my-account .box-recent .data-table { border-spacing:0; width:100%; }
.my-account .box-recent .data-table th,
.my-account .box-recent .data-table td { padding:2px 5px; }
.my-account .box-recent .data-table th {}
.my-account .box-recent .data-table tr:nth-child(odd)  {}
.my-account .box-recent .data-table tr td:nth-child(3) {}
.my-account .box-recent .data-table tr td:first-child a {}

.my-account .order-details .data-table { border-collapse:collapse; width:100%; }
.my-account .order-details .data-table th,
.my-account .order-details .data-table td { padding:10px; text-align:left; vertical-align:top; }
.my-account .order-details .data-table tbody tr:first-child { }
.my-account .order-details .data-table tbody th,
.my-account .order-details .data-table tbody td { border-bottom:1px solid #e4e4e4; }
.my-account .order-details .data-table tfoot { background:#f5f5f5; }

.my-account .product-review { padding:0; }
.my-account .product-review .ratings { text-align:center; }
.my-account .product-review .ratings .rating-box { display:inline-block; padding:15px 0; }
.my-account .product-review .ratings .rating-links { color:#424242; }
.my-account .product-review .product-details .product-name { color:#424242; font-size:18px; font-weight:bold; line-height:22px; margin:0 0 15px; }
.my-account .product-review .product-details .date,
.my-account .product-review .product-img-box > p,
.my-account .product-review .product-details .product-name + strong { display:none; }

.my-account .storecredit .account-balance { margin:0 0 10px; }

.customer-address-index .addresses-list ol { list-style:none; margin:0; padding:0; }

.my-account .my-rewards .info-box { position:relative; }
.my-account .my-rewards .info-box .box-content { padding-bottom:50px; }
.my-account .my-rewards .info-box .box-title a { position:absolute; bottom:10px; left:5px; }

/* Order
-----------------------------*/
.my-account .order-page .order-info {}

/* Wishlist
-----------------------------*/
.my-wishlist .data-table { border-spacing:0; border-collapse:collapse; width:100%; }
.my-wishlist .data-table th,
.my-wishlist .data-table td { padding:15px; text-align:left; vertical-align:top; }
.my-wishlist .data-table tr { }
.my-wishlist .data-table .select { display:none; }
.my-wishlist .data-table .btn-remove { float:right; }
.my-wishlist .data-table .product-name { font-size:18px; margin:3px 0 10px; }
.my-wishlist .data-table .product-image { float:left; margin:0 10px 10px 0; }
.my-wishlist .data-table .product-image img { display:block; margin:0 0 10px; }
.my-wishlist .data-table .price-box { margin:10px 0; }
.my-wishlist .data-table .comment { clear:left; margin:10px 0 0; }
.my-wishlist .data-table .btn-cart { background:transparent; border:none; color:#1394ca; font-size:15px; height:16px; display:inline-block; margin:0 5px; padding:0; text-decoration:underline; -webkit-appearance:none; vertical-align:middle; }
.my-wishlist .data-table .added-on { font-size:11px; }
.my-wishlist .data-table .qty { vertical-align:middle; width:50px; }
.my-wishlist .buttons-set { padding:15px 15px 10px; }
.my-wishlist .buttons-set:empty,
.my-wishlist .wishlist-empty + .buttons-set { display:none; }
.my-wishlist .buttons-set button { margin:10px 0 !important; }

.my-wishlist .cart-cell { display:table; margin:0 0 10px; }
.my-wishlist .item-manage { display:table; }
.my-wishlist .item-manage .split-button {
    background:#ececec;
    border:1px solid;
    border-color:#e4e4e4 #e4e4e4 #dcdcdc;
    border-radius:2px;
    color:#555;
    margin:0 0 10px;
    box-shadow:0 1px 3px #eee;
    position:relative;
}
.my-wishlist .item-manage .split-button .list-container {
    background:#fff;
    border:1px solid #e4e4e4;
    font-size:12px;
    display:none;
    top:100%;
    right:0;
    position:absolute;
    margin:2px 0 0;
    min-width:100%;
    white-space:nowrap;
    z-index:10;
    box-shadow:0 0 6px rgba(0, 0, 0, .15);
}
.my-wishlist .item-manage .split-button .list-container li {
    border-bottom:1px solid #e4e4e4;
    color:#1394ca;
    padding:8px;
}
.my-wishlist .item-manage .split-button .list-container li:last-child {
    border-bottom:none;
}
.my-wishlist .item-manage .split-button .list-container li.new { background:#e4e4e4; color:#424242; text-shadow:0 1px 0 #fff; }
.my-wishlist .item-manage .split-button .list-container li.new:before { content:'+'; display:inline-block; margin-right:5px; }

.my-wishlist .item-manage .split-button strong { background:#fff; border-radius:2px; display:block; border-right:1px solid #e4e4e4; font-size:12px; margin:0 25px 0 0; padding:5px 10px 5px 10px; }
.my-wishlist .item-manage .split-button strong:after {
    content:'';
    position:absolute;
    border-left:3px solid transparent;
    border-right:3px solid transparent;
    border-top:6px solid #555;
    position:absolute;
    font-size:0;
    line-height:0;
    width:0;
    top:11px;
    right:11px;
    z-index:2;
}
.my-wishlist .item-manage .split-button strong + a { display:block; text-align:center; line-height:28px; overflow:hidden; position:absolute; top:0; left:0; right:0; text-indent:-200%; }

.my-wishlist .truncated .details { display:none; }
.my-wishlist .truncated .item-options p { font-weight:bold; margin:10px 0; }

.wishlist-empty { padding:0 15px 15px; }

.item-manage .split-button {}
.item-manage .split-button .change { text-decoration:underline; }
.item-manage .split-button.active .change { color:#424242; text-decoration:none; }
.item-manage .split-button.active .list-container { display:block; }

.item-options { font-size:13px; }
.item-options .price { font-weight:bold; }
.item-options dt { color:#424242; font-weight:bold; margin:0 0 2px; }
.item-options dd + dt { margin:12px 0 0; }

.wishlist-management { padding:15px; }
.wishlist-management .sub-title h2 { display:inline-block; line-height:18px; position:relative; }
.wishlist-management .sub-title .item-count { margin:0 5px; }
.wishlist-management .table-caption .label { display:inline-block; vertical-align:top; }

#wishlists-move,
#wishlists-copy { display:none; }
#wishlists-select { display:inline-block; position:relative; }
#wishlists-select + p { margin:10px 0 0; }
#wishlists-select.active .list-container { display:block; }
#wishlists-select .list-container { background:#fff; border:1px solid #e4e4e4; display:none; margin:5px 0 0; position:absolute; white-space:nowrap; box-shadow:0 2px 6px rgba(0, 0, 0, .25); }
#wishlists-select .list-container li { border-bottom:1px solid #e4e4e4; }
#wishlists-select .list-container li:last-child { border-bottom:none; }
#wishlists-select .list-container li a { display:block; padding:5px 10px; }
#wishlists-select .list-container li.new { background:#e4e4e4; color:#424242; padding:5px 10px; text-shadow:0 1px 0 #fff; }
#wishlists-select .list-container li.new:before { content:'+'; display:inline-block; margin-right:5px; }

/* Gift Card Account */
.my-account .giftcardaccount .error-msg { background:#333; color:#fff; padding:15px; text-shadow:0 1px 0 #111; }
.my-account .giftcardaccount .please-wait { position:absolute; top:103px; left:103px; }
.my-account .giftcardaccount .buttons-set button:last-child { margin:10px 0 0 !important; }}

/* Cart
-----------------------------*/
.cart .page-title.title-buttons { height:auto; padding:0; }
.cart .page-title.title-buttons h1 { padding:10px 15px; }
.cart .page-title.title-buttons .checkout-types { padding:15px; }

.cart-table { border-spacing:0; border-collapse:collapse; width:100%; }
.cart-table td { padding:10px; vertical-align:top; }
.cart-table td:first-child { width:75px; }
.cart-table .price-box { display:table; width:100%; }
.cart-table .cart-price-box { display:table-cell; text-align:left; vertical-align:middle; }
.cart-table .cart-price-box .cart-price { font-size:12px; display:block; }
.cart-table .cart-price-box .cart-price + .cart-price { margin:5px 0 0; padding:5px 0 0; }
.cart-table .cart-price-box + .cart-price-box { margin-left:10px; text-align:right; }
.cart-table .cart-price-box + .cart-price-box .cart-price { color:#424242; font-size:15px; font-weight:bold; }
.cart-table .qty-wrap { display:table-cell; vertical-align:top; width:55px; }
.cart-table .qty { font-size:13px; text-align:center; vertical-align:middle; width:35px; }
.cart-table .qty + span { font-size:12px; margin:0 2px; vertical-align:middle; }
.cart-table .gift-registry-name { font-size:12px; margin:-12px 0 10px; }
.cart-table .btn-remove { float:right; position:relative; top:0; right:-5px; }
.cart-table .messages td { background-color:#444 !important; color:#fff; font-size:12px; padding:0 10px 10px; text-shadow:0 1px 0 #111; }
.cart-table .messages p { margin:10px 0 0; }
.cart-table .product-image { display:block; margin:0 0 10px; vertical-align:top; }
.cart-table .product-name { color:#424242; font-size:18px; line-height:22px; margin:0 0 15px; }
.cart-table .product-name a { color:#424242; }

.cart-table tfoot button + button { margin:10px 0 0; }

.cart-table .downloadable { margin:10px 0 0; }
.cart-table .product-options td { background:none; padding:0; }
.cart-table .product-options .toggle { color:#1394ca; cursor:pointer; display:inline-block; margin:10px; }
.cart-table .product-options .item-options { padding:10px; }

.cart-table .toggle { font-size:13px; display:inline-block; }

.cart-collaterals h2 { color:#424242; font-size:15px; font-weight:normal; line-height:18px; margin:0 0 5px; }
.cart-collaterals label { display:none; }
.cart-collaterals button { background:none; color:#1394ca; display:inline-block; border:0; font-size:15px; margin:0; padding:5px; text-decoration:underline; vertical-align:middle; }
.cart-collaterals button + button { padding:5px 0; }
.cart-collaterals .v-fix { display:inline-block; margin-right:5px; vertical-align:middle; width:60%; }
.cart-collaterals .v-fix + button { vertical-align:top; }

.cart-collaterals .discount { margin:0 0 10px; }
.cart-collaterals .giftcard .error-msg,
.cart-collaterals .giftcard .please-wait { display:block; font-size:12px; line-height:15px; margin:0 0 5px; }
.cart-collaterals .btn-gift-registry { margin:10px 0 0; }

.cart .totals,
.cart .checkout-types { padding:15px; }
.cart .checkout-types a { font-size:12px; display:block; margin:5px 0 0; padding:5px; text-decoration:underline; }
.cart .checkout-types li { margin:0 0 10px; text-align:center; }
.cart .checkout-types li .paypal-or { padding:5px 0; }

.cart .totals table { border-spacing:0; border-collapse:collapse; font-size:13px; width:100%; }
.cart .totals table th,
.cart .totals table td { padding:5px; }
.cart .totals table .price { white-space:nowrap; }
.cart .totals table .btn-remove { height:20px; width:20px; vertical-align:bottom; }
.cart .totals table .btn-remove img { display:none; }
.cart .totals table .summary-total .summary-collapse { display:inline-block; border-bottom:1px dashed; }
.cart .totals table .summary-total .summary-collapse:before { content:'+'; display:inline-block; margin-right:5px; }
.cart .totals table .summary-total.show-details .summary-collapse:before { content:'-'; }

.failed-products { border-bottom:1px solid #eae8ea; margin:10px 0 0; padding:0 0 10px; }
.failed-products h2 { font-weight:bold; }
.failed-products .cart-table thead { display:none; }

.checkout-success,
.paypal-review-order { padding:15px; }
.paypal-review-order select { width:100% !important; }
.paypal-review-order .sub-title { color:#424242; font-size:18px; line-height:22px; margin:0 0 15px; }
.paypal-review-order .legend { font-size:16px; line-height:20px; margin:15px 0; }
.paypal-review-order .info-set { margin:0 0 15px; }
.paypal-review-order .buttons-set button + button { margin:10px 0 0; }
.paypal-review-order .buttons-set .please-wait { display:block; font-size:12px; padding:10px; text-align:center; }

.paypal-review-order .data-table { border:1px solid #eee; border-spacing:0; border-collapse:collapse; font-size:13px; margin:15px 0; width:100%; }
.paypal-review-order .data-table tfoot { background:#eee; }
.paypal-review-order .data-table th { text-align:left; }
.paypal-review-order .data-table th:last-child { text-align:right; }
.paypal-review-order .data-table th { border-bottom:1px solid #eee; padding:5px; }
.paypal-review-order .data-table td { border-bottom:1px solid #eee; padding:5px; vertical-align:top; }

.checkout-success .sub-title { color:#424242; font-size:18px; line-height:22px; margin:0 0 15px; }
.checkout-success p { margin:10px 0; }

/* Checkout
-----------------------------*/
.opc { list-style:none; margin:0; padding:10px 10px 0; }

.opc p.required { font-size:10px; line-height:12px; margin:10px 0; text-align:right; }
.opc .back-link { display:none; }
.opc .v-middle { vertical-align:middle; }
.opc .please-wait { display:block; font-size:12px; padding:10px; text-align:center; }

.opc .step-title { background:url(../images/bg_gradient.png) repeat-x center #7f7f7f; color:#fff; height:30px; line-height:30px; padding:5px 8px; }
.opc .step-title h2 { font-size:15px; font-weight:bold; }
.opc .step-title a,
.opc .step-title .number { display:none; }

.opc .step { background-color:#fff !important; padding:10px; position:relative; }
.opc .step .form-list { margin:0 0 15px; }

.opc .section { margin:0 0 10px; opacity:.5; }
.opc .section.allow.active { opacity:1; }
.opc .section.allow .step-title h2:before {
    background:rgba(0, 0, 0, .5);
    border-radius:5px;
    content:'+';
    font-family:Arial;
    display:inline-block;
    line-height:15px;
    padding:5px 0;
    text-align:center;
    margin:2px 5px 0 0;
    vertical-align:top;
    width:20px;
}
.opc .section.allow.active .step-title h2:before { display:none; }

.checkout-onepage-payment-additional-giftcardaccount { margin:15px 0; }

#checkout-step-login h4 { color:#424242; margin:0 0 10px; }
#checkout-step-login p { font-size:14px; margin:0 0 10px; }

.opc .sp-methods > dt { color:#424242; font-size:15px; font-weight:bold; line-height:18px; margin:0 0 10px; }
.opc .sp-methods > dd + dt { margin:10px 0; }

.opc .tool-tip { background:#fff; position:absolute; padding:10px; -webkit-box-shadow:0 0 10px rgba(0, 0, 0, .5); }
.opc .tool-tip .btn-close { padding:0 0 15px 0; text-align:right; font-size:13px; }
.opc .tool-tip .btn-close a { color:#DF2327; }
.opc .tool-tip .tool-tip-content img { width:100%; }

.opc .cvv-what-is-this { border-bottom:1px dashed; display:inline-block; margin:5px 5px 0; font-size:13px; vertical-align:top; }

.opc li:last-child .step { padding:0; }

#checkoutSteps li .step .input-box .v-fix { display:inline-block; width:49%; }
#checkoutSteps li .step .input-box .v-fix .year { width:auto; }

.tool-tip .btn-close { padding:0 0 5px 0; text-align:right; font-size:13px; }
.tool-tip .btn-close a { color:#DF2327; }
.tool-tip .tool-tip-content img { width:100%; }

.opc .order-review .data-table { font-size:13px; border-spacing:0; border-collapse:collapse; margin:0 0 15px; width:100%; }
.opc .order-review .data-table thead tr,
.opc .order-review .data-table tbody tr:first-child { text-align:left; }
.opc .order-review .data-table tbody tr:last-child td:last-child { font-weight:bold; }
.opc .order-review .data-table th { text-align:left; }
.opc .order-review .data-table th:last-child { float:right; }
.opc .order-review .data-table th,
.opc .order-review .data-table td { padding:5px; }
.opc .order-review .data-table tfoot { background:#eee; }
.opc .order-review .data-table tfoot th { font-weight:normal; text-align:right; }
.opc .order-review .data-table .product-name { color:#424242; }
.opc .order-review .data-table .item-options { font-size:12px; margin:10px 0 0; }

.checkout-review-table { padding:0; }

.opc .order-review .buttons-set > p { margin:15px 0; }

#checkoutSteps #customerbalance_placer { padding:0 0 10px; }

.opc .gift-messages,
.opc .gift-messages-form { margin:10px 0 20px; }

.opc .gift-message-form .price,
.opc .gift-wrapping-form label { font-weight:bold; }
.opc .gift-wrapping-form label { color:#424242; }

.opc .gift-options-for-order { border:1px solid #e4e4e4; border-radius:2px; box-shadow:0 0 3px rgba(0, 0, 0, .15); margin:10px 0; padding:10px; }
.opc .gift-options-for-order div > a { display:inline-block; margin:10px 0 0; }
.opc .gift-options-for-order .fieldset { margin:10px 0 0; }

.opc .gift-wrapping-design { padding:10px 0 0; overflow:hidden; }
.opc .gift-wrapping-design .image-box { float:left; margin:0 10px 10px 0; }

.opc .gift-messages h3,
.opc .gift-messages-form h4 { color:#424242; font-weight:bold; line-height:18px; margin:0 0 10px; }
.opc .gift-messages-form p { margin:0 0 10px; }
.opc .gift-messages-form ol { list-style:none; margin:0 0 10px; padding:0; }

.opc .gift-messages-form .number { display:none; }
.opc .gift-messages-form .product-image { margin:0 0 2px; }
.opc .gift-messages-form .product-name { color:#424242; margin:0 0 10px; }

.extra-options-container { clear:both; }

.checkout-agreements { border:1px solid #e4e4e4; list-style:none; padding:10px; }
.checkout-agreements .agree { font-weight:bold; margin:10px 0 0; text-align:center; }
.checkout-agreements .agree input,
.checkout-agreements .agree label { margin-right:10px; vertical-align:middle; }

/* Advanced search
-----------------------------*/
.catalogsearch-advanced-result .search-summary,
.catalogsearch-advanced-result .page-title + p { display:none; }
.catalogsearch-advanced-result .toolbar { top:-100px; }

.catalogsearch-advanced-index .fieldset,
.catalogsearch-advanced-index .buttons-set { padding:15px; }
.catalogsearch-advanced-index .fieldset .legend { color:#424242; font-size:18px; line-height:22px; margin:0 0 15px; }

/* CMS */
.cms-home .std { display:none; }
.cms-home section { padding-bottom:0; }

.std ul,
.std ol,
.std dl,
.std p,
.std address,
.std blockquote,
.std table { margin-bottom:1em; }
.std ul { list-style:disc outside; padding-left:1.5em; }
.std ol { list-style:decimal outside; padding-left:1.5em; }
.std ul ul { list-style-type:circle; }
.std ul ul,
.std ol ol,
.std ul ol,
.std ol ul { margin:.5em 0; }
.std dt { font-weight:bold; }
.std dd { padding:0 0 0 1.5em; }
.std blockquote { font-style:italic; padding:0 0 0 1.5em; }
.std address { font-style:normal; }
.std b,
.std strong { font-weight:bold; }
.std i,
.std em { font-style:italic; }
.std hr { margin:1.5em 0; color:#d9d9d9; }
.std table { width:100%; max-width:100%; }
.std table thead { background:#e8e8e8; }
.std table th { background:#f7f7f7; word-break:break-word; font-weight:bold; }
.std table thead th { background:#e8e8e8; font-weight:bold; }
.std table th,
.std table td { padding:2px 4px; border:1px solid #d9d9d9; }
.std table[border="0"] { border-width:0; }
.std table[border="0"] th,
.std table[border="0"] td { border-width:0; }

ul.disc li { padding-left:10px; background:url(../images/bkg_bulletsm.gif) no-repeat 0 0.5em; }
.std ul.disc li { padding-left:0; background:none; }


/* Common Buttons Design */

.btn-checkout,
.account-login button,
.cart-table tfoot button,
.opc .buttons-set button,
.checkout-success button,
.box-reviews .buttons-set button,
.my-account form > .buttons-set button,
#contactForm .buttons-set button,
.giftregistry .buttons-set button,
.wishlist-view .buttons-set button,
.send-to-friend .buttons-set button,
.account-create .buttons-set button,
.catalog-product-gallery .buttons-set a,
.my-account .my-rewards .buttons-set button,
.my-account .my-wishlist .buttons-set button,
.popup-block .block-content .buttons-set button,
.customer-account-forgotpassword .buttons-set button,
.my-account .billing-agreements .box-content .form-list select + button {
    background:url(../images/bg_gradient.png) repeat-x center;
    color:#fff;
    font-size:21px;
    display:block;
    border:0;
    height:35px;
    line-height:35px;
    margin:0;
    padding:0;
    -webkit-appearance:none;
            appearance:none;
    text-shadow:0 -1px 0 rgba(0, 0, 0, .45);
    width:100%;
}

/* Theme options */

/* Custom colors */
/* Main color */
body > header,
body > footer ul,
body > address.copyright { background-color:#e76212; }

.map-popup-checkout button,
.more-button,
.btn-checkout,
.account-login button,
.add-to-links > li > a,
.cart-table tfoot button,
.opc .buttons-set button,
.checkout-success button,
.box-reviews .buttons-set button,
.my-account form > .buttons-set button,
#contactForm .buttons-set button,
.giftregistry .buttons-set button,
.wishlist-view .buttons-set button,
.send-to-friend .buttons-set button,
.account-create .buttons-set button,
.add-to-links > li .split-button strong,
.catalog-product-gallery .buttons-set a,
.my-account .my-rewards .buttons-set button,
.my-account .my-wishlist .buttons-set button,
.product-view .product-shop .add-to-box button,
.popup-block .block-content .buttons-set button,
.customer-account-forgotpassword .buttons-set button,
.product-view .product-options-bottom .add-to-cart button,
.my-account .billing-agreements .box-content .form-list select + button { background-color:#e76212; }

/* Links color */
#nav-container li a { color:#111; }

/* Buttons background color */
.block-subscribe button { background:green; }

body > header dd.menu-box a,
body > header dd.menu-box strong,
#nav-container li a,
.page-title,
.c-list > li,
.catalog-product-gallery .product-gallery,
.cart-table .messages td,
.opc .step,
.cart-empty,
body > section > form > .fieldset,
.account-login .col-1,
.account-login .col-2,
.account-create .fieldset,
.customer-account-forgotpassword .fieldset,
.customer-account-logoutsuccess .page-title + p,
.more,
.filters-block,
.product-view .product-shop,
.product-view .short-description,
.send-friend .fieldset,
.send-friend .buttons-set,
.product-collateral .box-collateral,
.product-collateral .box-additional .data-table th,
.product-collateral .box-additional .data-table td,
.box-up-sell,
.product-view .box-tags,
.product-view .options-container-big   .product-options,
.catalog-product-gallery .buttons-set,
.catalogsearch-result-index .note-msg,
.product-review,
.box-reviews .box-content,
.cart-collaterals,
.my-account > p,
.my-account > .col2-set,
.my-account > .order-info,
.my-account .fieldset,
.my-account .my-rewards,
.my-account .box-account,
.my-account .storecredit,
.my-account .order-history,
.my-account .order-details,
.my-account .review-history,
.my-account .giftregistry > p,
.my-account .recent-orders > p,
.my-account .review-history > p,
.my-account .recurring-profiles,
.my-account .billing-agreements,
.my-account .dashboard .reviews,
.my-account .order-page .order-info,
.my-account .order-page .order-date,
.my-account .product-review .product-details,
.my-account .dashboard .recent-orders,
.my-account .review-history .list-item,
.my-account .product-review .product-img-box,
.my-account .downloadable-products-history > p,
.my-account .downloadable-products-history .list-item,
.my-account .info-box .box-content,
.my-account .dashboard > .sub-title,
.my-account .review-history table td,
.my-account .recent-orders th,
.my-account .order-history table th,
.my-account .recent-orders td,
.my-account .order-history table td,
.my-account .order-details .data-table tbody tr:first-child,
.my-wishlist .data-table tr,
.my-wishlist .buttons-set,
.cart-table td,
.cart-table .product-options .item-options,
.cart .totals,
.cart .checkout-types,
.checkout-success,
.paypal-review-order,
.paypal-review-order .data-table,
.opc,
.opc .order-review .data-table thead tr,
.opc .order-review .data-table tbody tr:first-child { background:url(../images/bg_shadow.png) repeat-x 0 0; }

@media
only screen and (-webkit-min-device-pixel-ratio: 1.5),
only screen and (-o-min-device-pixel-ratio: 3/2),
only screen and (min--moz-device-pixel-ratio: 1.5),
only screen and (min-device-pixel-ratio: 1.5) {

    #nav-container li.subcategory-header { background-image:url(../images/bg_gradient_retina.png); background-size:auto 45px; }
    #nav-container li.subcategory-header .button-wrap button { background-image:url(../images/bg_back_btn_retina.png); background-size:47px 27px; }
    
    #nav-container li a span,
    .c-list > li > a { background-image:url(../images/custom/arrow_retina.png); background-size:7px 12px; }
    
    body > header dt { background-image:url(../images/bg_divider_retina.png); background-size:1px 45px; }
    body > header dt.menu a { background-image:url(../images/i_menu_retina.png); background-size:50%; }
    body > header dt.cart-icon a { background-image:url(../images/i_cart_retina.png); background-size:50%; }
    body > header { background-image:url(../images/bg_gradient_retina.png); background-size:auto 45px; }
    body > header .search input { background-image:url(../images/i_search_retina.png); background-size:17px 17px; }
    .header-bg { background:url(../images/custom/bg_header_retina.png) no-repeat center; background-size:auto 45px; }
    .header-bg .header-logo { background-image:url(../images/custom/bg_logo_retina.png); background-size:45px 45px; }
    
    .toolbar .sort-by > label { background-image:url(../images/bg_gradient_retina.png); }

    body > header dd.menu-box a { background-image:url(../images/bg_shadow_retina.png); background-size:1px 40px; }
    
    .rating-box { background-image:url(../images/i_star_blank_retina.png); background-size:20px 18px; }
    .rating-box .rating,
    #product-review-table tbody td input { background-image:url(../images/i_star_retina.png); background-size:20px 18px; }
    
    .controls { background-image:url(../images/bg_divider_dark_retina.png); background-size:1px 30px; }
    .controls .prev { background-image:url(../images/arrow_left_retina.png); background-size:7px 12px; }
    .controls .next { background-image:url(../images/arrow_right_retina.png); background-size:7px 12px; }
    
    select, .select-multiple { background-image:url(../images/i_dropdown_retina.png); background-size:30px auto; }
    
    a.btn-remove, .btn-remove2 { background-image:url(../images/btn_remove_retina.png); background-size:11px 11px; }
    a.link-edit { background-image:url(../images/btn_edit_retina.png); background-size:18px 18px; }
    
    .product-view .product-img-box .product-image:before { background-image:url(../images/i_zoom_retina.png); background-size:14px 14px; }

    .map-popup-checkout button,
    .btn-checkout,
    .account-login button,
    .cart-table tfoot button,
    .opc .buttons-set button,
    .checkout-success button,
    .box-reviews .buttons-set button,
    .my-account form > .buttons-set button,
    #contactForm .buttons-set button,
    .giftregistry .buttons-set button,
    .wishlist-view .buttons-set button,
    .send-to-friend .buttons-set button,
    .account-create .buttons-set button,
    .my-account .my-rewards .buttons-set button,
    .my-account .my-wishlist .buttons-set button,
    .popup-block .block-content .buttons-set button,
    .customer-account-forgotpassword .buttons-set button,
    .my-account .billing-agreements .box-content .form-list select + button {
        background-size:1px 35px;
    }
    
    body > header dd.menu-box a,
    body > header dd.menu-box strong,
    #nav-container li a,
    .page-title,
    .c-list > li,
    .catalog-product-gallery .product-gallery,
    .cart-table .messages td,
    .opc .step,
    .cart-empty,
    body > section > form > .fieldset,
    .account-login .col-1,
    .account-login .col-2,
    .account-create .fieldset,
    .customer-account-forgotpassword .fieldset,
    .customer-account-logoutsuccess .page-title + p,
    .more,
    .filters-block,
    .product-view .product-shop,
    .product-view .short-description,
    .send-friend .fieldset,
    .send-friend .buttons-set,
    .product-collateral .box-collateral,
    .product-collateral .box-additional .data-table th,
    .product-collateral .box-additional .data-table td,
    .box-up-sell,
    .product-view .box-tags,
    .product-view .options-container-big   .product-options,
    .catalog-product-gallery .buttons-set,
    .catalogsearch-result-index .note-msg,
    .product-review,
    .box-reviews .box-content,
    .cart-collaterals,
    .my-account > p,
    .my-account > .col2-set,
    .my-account > .order-info,
    .my-account .fieldset,
    .my-account .my-rewards,
    .my-account .box-account,
    .my-account .storecredit,
    .my-account .order-history,
    .my-account .order-details,
    .my-account .review-history,
    .my-account .giftregistry > p,
    .my-account .recent-orders > p,
    .my-account .review-history > p,
    .my-account .recurring-profiles,
    .my-account .billing-agreements,
    .my-account .dashboard .reviews,
    .my-account .order-page .order-info,
    .my-account .order-page .order-date,
    .my-account .product-review .product-details,
    .my-account .dashboard .recent-orders,
    .my-account .review-history .list-item,
    .my-account .product-review .product-img-box,
    .my-account .downloadable-products-history > p,
    .my-account .downloadable-products-history .list-item,
    .my-account .info-box .box-content,
    .my-account .dashboard > .sub-title,
    .my-account .review-history table td,
    .my-account .recent-orders th,
    .my-account .order-history table th,
    .my-account .recent-orders td,
    .my-account .order-history table td,
    .my-account .order-details .data-table tbody tr:first-child,
    .my-wishlist .data-table tr,
    .my-wishlist .buttons-set,
    .cart-table td,
    .cart-table .product-options .item-options,
    .cart .totals,
    .cart .checkout-types,
    .checkout-success,
    .paypal-review-order,
    .paypal-review-order .data-table,
    .opc,
    .opc .order-review .data-table thead tr,
    .opc .order-review .data-table tbody tr:first-child { background-image:url(../images/bg_shadow_retina.png); background-size:1px 40px; }
    
}
