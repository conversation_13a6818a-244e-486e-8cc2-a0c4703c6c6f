/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* Theme options */

/* Links Color */
a { color:#1394ca; }

/* Main color */
body > header,
body > footer ul,
body > address.copyright { background-color:#e76212; }

/* Button Color */
.map-popup-checkout button,
.more-button,
.btn-checkout,
.account-login button,
.add-to-links > li > a,
.cart-table tfoot button,
.opc .buttons-set button,
.checkout-success button,
.box-reviews .buttons-set button,
.my-account .form-buttons button,
#contactForm .buttons-set button,
.giftregistry .buttons-set button,
.wishlist-view .buttons-set button,
.send-to-friend .buttons-set button,
.account-create .buttons-set button,
.add-to-links > li .split-button strong,
.catalog-product-gallery .buttons-set a,
.my-account .my-rewards .buttons-set button,
.my-account .my-wishlist .buttons-set button,
.product-view .product-shop .add-to-box button,
.popup-block .block-content .buttons-set button,
.customer-account-forgotpassword .buttons-set button,
.product-view .product-options-bottom .add-to-cart button,
.my-account .billing-agreements .box-content .form-list select + button { background-color:#e76212; }

/* Main Page Navigation links color */
#nav-container li a { color:#111; }

/* Buttons background color */
.form-subscribe button { background:green; }
