/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_blank
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/* IE 6 only */
* html .validation-advice { height:13px; }
* html .error-msg,
* html .success-msg,
* html .note-msg,
* html .notice-msg { height:24px; }
.block li.item,
.block-poll li,
.opc li.section { vertical-align:top; }
* html .window-overlay {}

/* Doubled Margin Fixes */
.product-view .product-img-box .more-views li,
.product-view .box-tags .form-add .input-box,
.sp-methods select.month { display:inline; }

/********** < Navigation styles */
#nav li,
#nav li a { zoom:1; }
#nav li { vertical-align:top; }
/********** < Navigation styles */

select { margin-bottom:1px; }
input.radio { width:13px; height:13px; }
input.checkbox { width:13px; height:13px; }
button.button { height:21px; }
button.button span { position:relative; }
button.btn-checkout {}
.form-list label { position:relative; z-index:0; }
.form-list label.required em { position:absolute; top:0; right:-8px; }

/*table { scrollbar-face-color:expression(runtimeStyle.scrollbarFaceColor = '#fff', cellSpacing = 0); }*/ /* Cellspacing fix for IE6-7 */

.tool-tip .btn-close a { margin:0; }

.product-view .product-img-box .zoom.disabled { filter:alpha(opacity=30); }

/* Clearer */
.clearer { display:block; clear:both; font-size:0; line-height:0; height:0; overflow:hidden; }

/* Clears and hasLayout fixes */
.header-container,
.header-container .top-container,
.header,
.header .quick-access,
#nav,
.main,
.footer,
.footer-container .bottom-container,
.col-main,
.col2-set,
.col3-set,
.col4-set,
.messages li,
.search-autocomplete li,
.block,
.block .block-content,
.block .actions,
.block li.item,
.block-poll li,
.block-poll .label,
.block-layered-nav .currently li,
.block-account .block-content li a,
.mini-products-list .product-details,
.page-title,
.rss-title h1,
.products-grid,
.products-list li.item,
.box-account .box-head,
.dashboard .box .box-title,
.box-reviews li.item,
.box-tags li.item,
.pager,
.sorter,
.ratings,
.add-to-box,
.add-to-cart,
.product-essential,
.product-collateral,
.product-view .product-img-box .more-views ul,
.product-view .product-shop .short-description,
.product-view .box-description,
.product-view .box-tags .form-add,
.product-options .options-list li,
.product-options-bottom,
.truncated,
.truncated .truncated_full_value,
.product-review,
.cart,
.cart-collaterals,
.cart .crosssell li.item,
.cart .discount,
.opc .step-title,
.opc .step,
.multiple-checkout,
.sp-methods,
.checkout-progress,
.multiple-checkout .place-order,
.form-list li,
.form-list .field,
.group-select li,
.buttons-set,
.page-print .print-head,
.cart-tax-total,
.advanced-search-summary,
.advanced-search-summary p,
.gift-messages-form .item,
.send-friend .form-list li p,
.centinel .authentication { zoom:1; }

/* Hover Fix */
iframe.hover-fix { position:absolute; left:-1px; top:-1px; z-index:-1; background:transparent; filter:progid:DXImageTransform.Microsoft.Alpha(style=0,opacity=0); }
