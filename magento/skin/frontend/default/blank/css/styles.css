/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_blank
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/* Reset ================================================================================= */
* { margin:0; padding:0; }

body          { background:#fff; font:12px/1.35 Arial, Helvetica, sans-serif; color:#000; text-align:center; }

img           { border:0; vertical-align:top; }

a             { color:#05c; text-decoration:underline; }
a:hover       { text-decoration:none; }
:focus        { outline:0; }

/* Headings */
h1            { font-size:20px; font-weight:normal; line-height:1.15; }
h2            { font-size:18px; font-weight:normal; line-height:1.25; }
h3            { font-size:16px; font-weight:bold; line-height:1.25; }
h4            { font-size:14px; font-weight:bold; }
h5            { font-size:12px; font-weight:bold; }
h6            { font-size:11px; font-weight:bold; }

/* Forms */
form          { display:inline; }
fieldset      { border:0; }
legend        { display:none; }

/* Table */
table         { border:0; border-collapse:collapse; border-spacing:0; empty-cells:show; font-size:100%; }
caption,th,td { vertical-align:top; text-align:left; font-weight:normal; }

/* Content */
strong        { font-weight:bold; }
address       { font-style:normal; }
cite          { font-style:normal; }
q,
blockquote    { quotes:none; }
q:before,
q:after       { content:''; }
small,big     { font-size:1em; }
sup           { font-size:1em; vertical-align:top; }

/* Lists */
ul,ol         { list-style:none; }

/* Tools */
.hidden       { display:block !important; border:0 !important; margin:0 !important; padding:0 !important; font-size:0 !important; line-height:0 !important; width:0 !important; height:0 !important; overflow:hidden !important; }
.nobr         { white-space:nowrap !important; }
.wrap         { white-space:normal !important; }
.a-left       { text-align:left !important; }
.a-center     { text-align:center !important; }
.a-right      { text-align:right !important; }
.v-top        { vertical-align:top; }
.v-middle     { vertical-align:middle; }
.f-left,
.left         { float:left !important; }
.f-right,
.right        { float:right !important; }
.f-none       { float:none !important; }
.f-fix        { float:left; width:100%; }
.no-display   { display:none; }
.no-margin    { margin:0 !important; }
.no-padding   { padding:0 !important; }
.no-bg        { background:none !important; }
/* ======================================================================================= */


/* Layout ================================================================================ */
.wrapper {}
.page { width:1000px; margin:0 auto; padding:10px 0; text-align:left; }
.page-print { background:#fff; padding:20px; text-align:left; }
.page-empty { background:#fff; padding:20px; text-align:left; }
.page-popup { padding:20px; text-align:left; }
.main-container {}
.main { margin:10px 0; }

/* Base Columns */
.col-left { float:left; width:230px; border:1px solid #ddd; padding:5px 5px 0; }
.col-main { float:left; width:736px; border:1px solid #ddd; padding:5px; }
.col-right { float:right; width:230px; border:1px solid #ddd; padding:5px 5px 0; }

/* 1 Column Layout */
.col1-layout .col-main { float:none; width:auto; }

/* 2 Columns Layout */
.col2-left-layout .col-main { float:right; }
.col2-right-layout .col-main {}

/* 3 Columns Layout */
.col3-layout .col-main { width:484px; margin-left:10px; }
.col3-layout .col-wrapper { float:left; width:748px; }
.col3-layout .col-wrapper .col-main { float:right; }

/* Content Columns */
.col2-set .col-1 { float:left; width:49%; }
.col2-set .col-2 { float:right; width:49%; }
.col2-set .col-narrow { width:33%; }
.col2-set .col-wide { width:65%; }

.col3-set .col-1 { float:left; width:32%; }
.col3-set .col-2 { float:left; width:32%; margin-left:2%; }
.col3-set .col-3 { float:right; width:32%; }

.col4-set .col-1 { float:left; width:23.5%; }
.col4-set .col-2 { float:left; width:23.5%; margin:0 2%; }
.col4-set .col-3 { float:left; width:23.5%; }
.col4-set .col-4 { float:right; width:23.5%; }
/* ======================================================================================= */


/* Global Styles ========================================================================= */
/* Form Elements */
input,select,textarea,button { font:12px Arial, Helvetica, sans-serif; vertical-align:middle; color:#000; }
input.input-text,select,textarea { background:#fff; border:1px solid #ddd; }
input.input-text,textarea { padding:2px; }
select { padding:1px; }
select option { padding-right:10px; }
select.multiselect option { border-bottom:1px solid #ddd; padding:2px 5px; }
select.multiselect option:last-child { border-bottom:0; }
textarea { overflow:auto; }
input.radio { margin-right:3px; }
input.checkbox { margin-right:3px; }
input.qty { width:2.5em !important; }
button.button::-moz-focus-inner { padding:0; border:0; } /* FF Fix */
button.button { -webkit-border-fit:lines; } /* <- Safari & Google Chrome Fix */
button.button { overflow:visible; width:auto; border:0; padding:0; margin:0; background:transparent; cursor:pointer; }
button.button span { float:left; height:21px; background:transparent url(../images/bkg_button.gif) 0 0 no-repeat; padding:0 0 0 8px; font:bold 12px/21px Arial, Helvetica, sans-serif; text-align:center; white-space:nowrap; color:#fff; }
button.button span span { background-position:100% 0; padding:0 12px 0 4px; }
button.disabled {}
button.disabled span {}

button.btn-checkout span {}
button.btn-checkout.no-checkout {}

p.control input.checkbox,
p.control input.radio { margin-right:6px; }
/* Form Highlight */
/*input.input-text:focus,select:focus,textarea:focus {}*/
/*.highlight { background:#efefef; }*/

/* Form lists */
/* Grouped fields */
/*.form-list { width:535px; margin:0 auto; overflow:hidden; }*/
.form-list li { margin:0 0 8px; }
.form-list label { float:left; color:#111; font-weight:bold; position:relative; z-index:0; }
.form-list label.required {}
.form-list label.required em { float:right; font-style:normal; color:#eb340a; position:absolute; top:0; right:-8px; }
.form-list li.control label { float:none; }
.form-list li.control input.radio,
.form-list li.control input.checkbox { margin-right:6px; }
.form-list li.control .input-box { clear:none; display:inline; width:auto; }
/*.form-list li.fields { margin-right:-15px; }*/
.form-list .input-box { display:block; clear:both; width:260px; }
.form-list .field { float:left; width:275px; }
.form-list input.input-text { width:254px; }
.form-list textarea { width:254px; height:10em; }
.form-list select { width:260px; }
.form-list li.wide .input-box { width:535px; }
.form-list li.wide input.input-text { width:529px; }
.form-list li.wide textarea { width:529px; }
.form-list li.wide select { width:535px; }
.form-list li.additional-row { border-top:1px solid #ccc; margin-top:10px; padding-top:7px; }
.form-list li.additional-row .btn-remove { float:right; margin:5px 0 0; }
.form-list .input-range input.input-text { width:74px; }
/* Customer */
.form-list .customer-name-prefix .input-box,
.form-list .customer-name-suffix .input-box,
.form-list .customer-name-prefix-suffix .input-box,
.form-list .customer-name-prefix-middlename .input-box,
.form-list .customer-name-middlename-suffix .input-box,
.form-list .customer-name-prefix-middlename-suffix .input-box { width:auto; }

.form-list .name-prefix { width:65px; }
.form-list .name-prefix select { width:55px; }
.form-list .name-prefix input.input-text { width:49px; }

.form-list .name-suffix { width:65px; }
.form-list .name-suffix select { width:55px; }
.form-list .name-suffix input.input-text { width:49px; }

.form-list .name-middlename { width:70px; }
.form-list .name-middlename input.input-text { width:49px; }

.form-list .customer-name-prefix-middlename-suffix .name-firstname,
.form-list .customer-name-prefix-middlename .name-firstname { width:140px; }
.form-list .customer-name-prefix-middlename-suffix .name-firstname input.input-text,
.form-list .customer-name-prefix-middlename .name-firstname input.input-text { width:124px; }
.form-list .customer-name-prefix-middlename-suffix .name-lastname { width:205px; }
.form-list .customer-name-prefix-middlename-suffix .name-lastname input.input-text { width:189px; }

.form-list .customer-name-prefix-suffix .name-firstname { width:210px; }
.form-list .customer-name-prefix-suffix .name-lastname { width:205px; }
.form-list .customer-name-prefix-suffix .name-firstname input.input-text,
.form-list .customer-name-prefix-suffix .name-lastname input.input-text { width:189px; }

.form-list .customer-name-prefix-suffix .name-firstname { width:210px; }
.form-list .customer-name-prefix-suffix .name-lastname { width:205px; }
.form-list .customer-name-prefix-suffix .name-firstname input.input-text,
.form-list .customer-name-prefix-suffix .name-lastname input.input-text { width:189px; }

.form-list .customer-name-prefix .name-firstname,
.form-list .customer-name-middlename .name-firstname { width:210px; }

.form-list .customer-name-suffix .name-lastname,
.form-list .customer-name-middlename .name-firstname,
.form-list .customer-name-middlename-suffix .name-firstname,
.form-list .customer-name-middlename-suffix .name-lastname { width:205px; }

.form-list .customer-name-prefix .name-firstname input.input-text,
.form-list .customer-name-suffix .name-lastname input.input-text,
.form-list .customer-name-middlename .name-firstname input.input-text,
.form-list .customer-name-middlename-suffix .name-firstname input.input-text,
.form-list .customer-name-middlename-suffix .name-lastname input.input-text { width:189px; }

.form-list .customer-dob .dob-month,
.form-list .customer-dob .dob-day,
.form-list .customer-dob .dob-year { float:left; width:85px; }
.form-list .customer-dob input.input-text { display:block; width:74px; }
.form-list .customer-dob label { font-size:10px; font-weight:normal; color:#888; }
.form-list .customer-dob .dob-day,
.form-list .customer-dob .dob-month { width:60px; }
.form-list .customer-dob .dob-day input.input-text,
.form-list .customer-dob .dob-month input.input-text { width:46px; }
.form-list .customer-dob .dob-year { width:140px; }
.form-list .customer-dob .dob-year input.input-text { width:134px; }

.buttons-set { clear:both; margin:5px 0 0; padding:5px; border:1px solid #ddd; }
.buttons-set .back-link { float:left; }
.buttons-set button.button { float:right; }
.buttons-set p.required { margin:0 0 5px; }

.buttons-set-order {}

.fieldset { border:1px solid #ddd; background:#fefefe; padding:22px 25px 12px 33px; margin:28px 0; }
.fieldset .legend { float:left; font-weight:bold; font-size:13px; border:1px solid #fefefe; background:#dedede; color:#333; margin:-33px 0 0 -10px; padding:0 8px; position:relative; }

/* Form Validation */
.validation-advice { clear:both; min-height:13px; margin:3px 0 0; padding-left:17px; font-size:10px; line-height:13px; background:url(../images/validation_advice_bg.gif) 2px 1px no-repeat; color:#f00; }
.validation-failed { border:1px dashed #f00 !important; background:#faebe7 !important; }
.validation-passed {}
p.required { font-size:10px; text-align:right; color:#f00; }
/* Expiration date and CVV number validation fix */
.v-fix { float:left; }
.v-fix .validation-advice { display:block; width:12em; margin-right:-12em; position:relative; }

/* Global Messages  */
.success { color:#3d6611; font-weight:bold; }
.error { color:#f00; font-weight:bold; }
.notice { color:#ccc; }

.messages,
.messages ul { list-style:none !important; margin:0 !important; padding:0 !important; }
.messages { width:100%; overflow:hidden; }
.messages li { margin:0 0 10px; }
.messages li li { margin:0 0 3px; }
.error-msg,
.success-msg,
.note-msg,
.notice-msg { border-style:solid !important; border-width:1px !important; background-position:10px 9px !important; background-repeat:no-repeat !important; min-height:24px !important; padding:8px 8px 8px 32px !important; font-size:11px !important; font-weight:bold !important; }
.error-msg { border-color:#f16048; background-color:#faebe7; background-image:url(../images/i_msg-error.gif); color:#df280a; }
.success-msg { border-color:#446423; background-color:#eff5ea; background-image:url(../images/i_msg-success.gif); color:#3d6611; }
.note-msg,
.notice-msg { border-color:#fcd344; background-color:#fafaec; background-image:url(../images/i_msg-note.gif); color:#3d6611; }

/* BreadCrumbs */
.breadcrumbs { padding:10px; margin:0 0 10px; border:1px solid #ddd; }
.breadcrumbs li { display:inline; }

/* Page Heading */
.page-title { border-bottom:1px solid #ddd; padding:0 0 5px; margin:0 0 10px; }
.page-title h1,
.page-title h2 { font-size:18px; color:#000; }
.page-title .separator { margin:0 3px; }
.page-title .link-rss { float:right; }
.title-buttons { text-align:right; }
.title-buttons h1,
.title-buttons h2,
.title-buttons h3,
.title-buttons h4,
.title-buttons h5,
.title-buttons h6 { float:left; }

.subtitle,
.sub-title { clear:both; }

/* Pager */
.pager { border:1px solid #ddd; padding:5px; margin:5px 0; text-align:center; }
.pager .amount { float:left; }
.pager .limiter { float:right; }
.pager .pages { margin:0 135px; }
.pager .pages ol { display:inline; }
.pager .pages li { display:inline; }
.pager .pages .current {}

/* Sorter */
.sorter { border:1px solid #ddd; padding:5px; margin:5px 0; }
.sorter .view-mode { float:left; }
.sorter .sort-by { float:right; }
.sorter .link-feed {}

/* Toolbar */
.toolbar {}
.toolbar .pager {}
.toolbar .sorter {}
.toolbar-bottom {}

/* Data Table */
.data-table { width:100%; }
.data-table th { padding:5px; border:1px solid #ddd; font-weight:bold; white-space:nowrap; }
.data-table td { padding:5px; border:1px solid #ddd; }
.data-table thead { background-color:#f2f2f2; }
.data-table tbody {}
.data-table tfoot {}
.data-table tr.first {}
.data-table tr.last {}
.data-table tr.odd {}
.data-table tr.even { background-color:#f6f6f6; }
.data-table tbody.odd {}
.data-table tbody.odd td { border-width:0 1px; }
.data-table tbody.even { background-color:#f6f6f6; }
.data-table tbody.even td { border-width:0 1px; }
.data-table tbody.odd tr.border td,
.data-table tbody.even tr.border td { border-bottom-width:1px; }
.data-table th .tax-flag { white-space:nowrap; font-weight:normal; }
.data-table td.label,
.data-table th.label { font-weight:bold; background-color:#f6f6f6; }
.data-table td.value {}

/* Shopping cart total summary row expandable to details */
tr.summary-total { cursor:pointer; }
tr.summary-total td {}
tr.summary-total .summary-collapse { float:right; text-align:right; padding-left:20px; background:url(../images/bkg_collapse.gif) 0 4px no-repeat; cursor:pointer; }
tr.show-details .summary-collapse { background-position:0 -53px; }
tr.show-details td {}
tr.summary-details td { font-size:11px; background-color:#dae1e4; color:#626465; }
tr.summary-details-first td { border-top:1px solid #d2d8db; }
tr.summary-details-excluded { font-style:italic; }

/* Shopping cart tax info */
.cart-tax-info { display:block; }
.cart-tax-info,
.cart-tax-info .cart-price { padding-right:20px; }
.cart-tax-total { display:block; padding-right:20px; background:url(../images/bkg_collapse.gif) 100% 4px no-repeat; cursor:pointer; }
.cart-tax-info .price,
.cart-tax-total .price { display:inline !important; font-weight:normal !important; }
.cart-tax-total-expanded { background-position:100% -53px; }

/* Class: std - styles for admin-controlled content */
.std .subtitle { padding:0; }
.std ol.ol { list-style:decimal outside; padding-left:1.5em; }
.std ul.disc { list-style:disc outside; padding-left:18px; margin:0 0 10px; }
.std dl dt { font-weight:bold; }
.std dl dd { margin:0 0 10px; }
.std ul,
.std ol,
.std dl,
.std p,
.std address,
.std blockquote { margin:0 0 1em; padding:0; }
.std ul { list-style:disc outside; padding-left:1.5em; }
.std ol { list-style:decimal outside; padding-left:1.5em; }
.std ul ul { list-style-type:circle; }
.std ul ul,
.std ol ol,
.std ul ol,
.std ol ul { margin:.5em 0; }
.std dt { font-weight:bold; }
.std dd { padding:0 0 0 1.5em; }
.std blockquote { font-style:italic; padding:0 0 0 1.5em; }
.std address { font-style:normal; }
.std b,
.std strong { font-weight:bold; }
.std i,
.std em { font-style:italic; }

/* Misc */
.links li { display:inline; }
.links li.first { padding-left:0 !important; }
.links li.last { background:none !important; padding-right:0 !important; }

.link-cart { font-weight:bold; color:#f00; }
.link-wishlist { font-weight:bold; }
.link-reorder { font-weight:bold; }
.link-compare { font-weight:bold; }
.link-print { background:url(../images/i_print.gif) 0 2px no-repeat; padding:2px 0 2px 25px; }
.link-rss { background:url(../images/i_rss.gif) 0 1px no-repeat; padding-left:18px; white-space:nowrap; }
.btn-remove { display:block; width:11px; height:11px; font-size:0; line-height:0; background:url(../images/btn_remove.gif) 0 0 no-repeat; text-indent:-999em; overflow:hidden; }
.btn-remove2 { display:block; width:16px; height:16px; font-size:0; line-height:0; background:url(../images/btn_trash.gif) 0 0 no-repeat; text-indent:-999em; overflow:hidden; }
.btn-edit    { display:block; width:11px; height:11px; font-size:0; line-height:0; background:url(../images/btn_edit.gif) 0 0 no-repeat; text-indent:-999em; overflow:hidden; }

.cards-list dt { margin:5px 0 0; }
.cards-list .offset { padding:2px 0 2px 20px; }


.separator { margin:0 3px; }

.divider { clear:both; display:block; font-size:0; line-height:0; height:1px; margin:10px 0; background:#ddd; text-indent:-999em; overflow:hidden; }

/* Noscript Notice */
.noscript { border:1px solid #ddd; border-width:0 0 1px; background:#ffff90; font-size:12px; line-height:1.25; text-align:center; color:#2f2f2f; }
.noscript .noscript-inner { width:1000px; margin:0 auto; padding:12px 0 12px; background:url(../images/i_notice.gif) 20px 50% no-repeat; }
.noscript p { margin:0; }

/* Demo Notice */
.demo-notice { margin:0; padding:6px 10px; background:#d75f07; font-size:12px; line-height:1.15; text-align:center; color:#fff; }

/* Cookie Notice */
.notice-cookie { border-bottom:1px solid #cfcfcf; background:#ffff90; font-size:12px; line-height:1.25; text-align:center; color:#2f2f2f; }
.notice-cookie .notice-inner { width:870px; margin:0 auto; padding:12px 0 12px 80px; background:url(../images/i_notice.gif) 20px 25px no-repeat; text-align:left; }
.notice-cookie .notice-inner p { margin:0 0 10px; border:1px dotted #cccc73; padding:10px; }
.notice-cookie .notice-inner .actions { }

/* ======================================================================================= */


/* Header ================================================================================ */
.logo { float:left; }
.header-container {}
.header { padding:10px; border:1px solid #ddd; }
.header .logo { float:left; text-decoration:none !important; }
.header .logo strong { position:absolute; top:-999em; left:-999em; width:0; height:0; font-size:0; line-height:0; text-indent:-999em; overflow:hidden; }
.header h1.logo { margin:0; padding:0; }
.header .welcome-msg { font-weight:bold; text-align:right; }
.header .welcome-msg a {}
.header .links { float:right; }
.header .form-search { text-align:right; }
.header .form-search .search-autocomplete { z-index:999; }
.header .form-search .search-autocomplete ul { border:1px solid #ddd; background-color:#fff; }
.header .form-search .search-autocomplete li { padding:3px; border-bottom:1px solid #ddd; cursor:pointer; }
.header .form-search .search-autocomplete li .amount { float:right; font-weight:bold; }
.header .form-search .search-autocomplete li.selected {}
.header .form-language { clear:both; text-align:right; }
.header-container .top-container { clear:both; text-align:right; }

/********** < Navigation */
.nav-container {}
#nav { border:1px solid #ddd; padding:5px 0; margin:10px 0; font-size:13px; }

/* All Levels */
#nav li { text-align:left; position:relative; }
#nav li.over { z-index:998; }
#nav li.parent {}
#nav li a { display:block; text-decoration:none; }
#nav li a:hover { text-decoration:none; }
#nav li a span { display:block; white-space:nowrap; cursor:pointer; }
#nav li ul a span { white-space:normal; }

/* 1st Level */
#nav li { float:left; }
#nav li a { float:left; padding:5px 10px; font-weight:bold; color:#888; }
#nav li a:hover { color:#000; }
#nav li.over a,
#nav li.active a { color:#000; }

/* 2nd Level */
#nav ul,
#nav div { position:absolute; width:15em; top:25px; left:-10000px; border:1px solid #ddd; background:#fafafa; }
#nav div ul { position:static; width:auto; border:none; }

/* 3rd+ leven */
#nav ul ul,
#nav ul div { top:7px; }

#nav ul li { float:none; border-bottom:1px solid #ddd; }
#nav ul li.last { border-bottom:0; }
#nav ul li a { float:none; padding:3px 9px; font-weight:normal; color:#888 !important; }
#nav ul li a:hover { color:#000 !important; }
#nav ul li.active > a,
#nav ul li.over > a { color:#000 !important; }

/* Show menu */
#nav li ul.shown-sub,
#nav li div.shown-sub { left:0; z-index:999; }
#nav li .shown-sub ul.shown-sub,
#nav li .shown-sub li div.shown-sub { left:100px; }
/********** Navigation > */
/* ======================================================================================= */


/* Sidebar =============================================================================== */
.block { border:1px solid #ddd; margin:0 0 10px; }
.block .block-title { border-bottom:1px solid #ddd; padding:3px 10px; }
.block .block-title strong { font-size:14px; font-weight:bold; }
.block .block-title strong span {}
.block .block-title a { text-decoration:none !important; }
.block .block-subtitle { font-size:13px; font-weight:bold; }
.block .block-content { padding:10px; }
.block .block-content li.item { padding:5px 0; }
.block .btn-remove,
.block .btn-edit { float:right;}
.block .actions { text-align:right; }
.block .actions a { float:left; }
.block .empty {}

.block li.odd {}
.block li.even { background-color:#f6f6f6; }

/* Mini Products List */
.mini-products-list li { padding:5px 0; }
.mini-products-list .product-image { float:left; width:50px; padding:5px; border:1px solid #ddd; }
.mini-products-list .product-details { margin-left:75px; }
.mini-products-list .product-details h4 { font-size:1em; font-weight:bold; margin:0; }
.block-cart .mini-products-list .product-details .product-name,
.block-cart .mini-products-list .product-details .nobr small { word-wrap:break-word; }
.block-cart .mini-products-list .product-details .nobr { white-space:normal !important; }

/* Block: Account */
.block-account {}

/* Block: Currency Switcher */
.block-currency {}
.block-currency select { width:100%; border:1px solid #888; }

/* Block: Layered Navigation */
.block-layered-nav {}
.block-layered-nav dt { font-weight:bold; }
.block-layered-nav .currently {}
.block-layered-nav .btn-remove { float:left; margin:2px 3px 0 0; }

/* Block: Cart */
.block-cart {}
.block-cart .summary {}
.block-cart .amount {}
.block-cart .subtotal { text-align:center; }
.block-cart .actions .paypal-logo { float:left; width:100%; margin:3px 0 0; text-align:right; }
.block-cart .actions .paypal-logo .paypal-or { clear:both; display:block; padding:0 55px 5px 0; }

/* Block: Wishlist */
.block-wishlist {}

/* Block: Related */
.block-related {}
.block-related li { padding:5px 0; }
.block-related input.checkbox { float:left; margin-right:-20px; }
.block-related .product { margin-left:20px; }
.block-related .product .product-image { float:left; margin-right:-65px; }
.block-related .product .product-details { margin-left:65px; }

/* Block: Compare Products */
.block-compare {}
.block-compare li { padding:5px 0; }

/* Block: Recently Viewed */
.block-viewed {}

/* Block: Recently Compared */
.block-compared {}

/* Block: Poll */
.block-poll label {}
.block-poll input.radio { float:left; margin:1px -18px 0 0; }
.block-poll .label { display:block; margin-left:18px; }
.block-poll li { padding:3px 9px; }
.block-poll .actions { margin:5px 0 0; }
.block-poll .answer { font-weight:bold; }
.block-poll .votes { float:right; margin-left:10px; }

/* Block: Tags */
.block-tags ul,
.block-tags li { display:inline; }

/* Block: Subscribe */
.block-subscribe {}

/* Block: Reorder */
.block-reorder {}
.block-reorder li { padding:5px 0; }
.block-reorder input.checkbox { float:left; margin:3px -20px 0 0; }
.block-reorder .product-name { margin-left:20px; }

/* Block: Banner */
.block-banner {}
.block-banner .block-content { text-align:center; }

/* Block: Login */
.block-login label { font-weight:bold; color:#666; }
.block-login input.input-text { display:block; width:167px; margin:3px 0; }

/* Paypal */
.sidebar .paypal-logo { display:block; margin:10px 0; text-align:center; }
.sidebar .paypal-logo a { float:none; }
.paypal-express-review .info-set.col2-set { padding-bottom: 15px; }
/* ======================================================================================= */


/* Category Page ========================================================================= */
.category-title { border:0; margin:0 0 7px; }
.category-image { width:100%; overflow:hidden; margin:0 0 10px; text-align:center; }
.category-image img {}
.category-description { margin:0 0 10px; }
.category-products {}

/* View Type: Grid */
.products-grid { border-bottom:1px solid #ddd; position:relative; }
.products-grid.last { border-bottom:0; }
.products-grid li.item { float:left; width:138px; padding:12px 10px 80px; }
.products-grid .product-image { display:block; width:135px; height:135px; margin:0 0 10px; }
.products-grid .product-name { margin:0 0 5px; font-weight:bold; font-size:13px; }
.products-grid .product-name a {}
.products-grid .price-box { margin:5px 0; }
.products-grid .availability { line-height:21px; }
.products-grid .actions { position:absolute; bottom:12px; }
.col2-left-layout .products-grid,
.col2-right-layout .products-grid { width:632px; margin:0 auto; }
.col1-layout .products-grid { width:790px; margin:0 auto; }

/* View Type: List */
.products-list li.item { border-bottom:1px solid #ddd; padding:12px 10px; }
.products-list li.item.last { border-bottom:0; }
.products-list .product-image { float:left; width:135px; height:135px; margin:0 0 10px; }
.products-list .product-shop { margin-left:150px; }
.products-list .product-name { margin:0 0 5px; font-weight:bold; font-size:13px; }
.products-list .product-name a {}
.products-list .price-box { float:left; margin:3px 13px 5px 0; }
.products-list .availability { float:left; margin:3px 0 0; }
.products-list .desc { clear:both; padding:6px 0 0; margin:0 0 15px; line-height:1.35; }
.products-list .desc .link-learn { font-size:11px; }
.products-list .add-to-links { clear:both; }
.products-list .add-to-links li { display:inline; }
.products-list .add-to-links .separator { display:inline; margin:0 2px; }
/* ======================================================================================= */


/* Product View ========================================================================== */
/* Rating */
.no-rating { margin:0; }

.ratings { font-size:11px; line-height:1.25; margin:7px 0; }
.ratings strong { float:left; margin:1px 3px 0 0; }
.ratings .rating-links { margin:0; }
.ratings .rating-links .separator { margin:0 2px; }
.ratings dt {}
.ratings dd {}
.rating-box { width:69px; height:13px; font-size:0; line-height:0; background:url(../images/bkg_rating.gif) 0 0 repeat-x; text-indent:-999em; overflow:hidden; }
.rating-box .rating { float:left; height:13px; background:url(../images/bkg_rating.gif) 0 100% repeat-x; }
.ratings .rating-box { float:left; margin-right:3px; }
.ratings .amount {}

.ratings-table th,
.ratings-table td { font-size:11px; line-height:1.15; padding:3px 0; }
.ratings-table th { font-weight:bold; padding-right:8px; }

/* Availability */
.availability { margin:0; }
.availability span { font-weight:bold; }
.availability.in-stock span {}
.availability.out-of-stock span { color:#d83820; }

.availability-only { margin:0 0 7px; }
.availability-only a { background:url(../images/i_availability_only_arrow.gif) 100% 0 no-repeat; cursor:pointer; padding-right:15px; }
.availability-only .expanded { background-position:100% -15px; }
.availability-only strong {}

.availability-only-details { margin:0 0 7px; }
.availability-only-details th { background:#f2f2f2; font-size:10px; padding:0 8px; }
.availability-only-details td { border-bottom:1px solid #ddd; font-size:11px; padding:2px 8px 1px; }
.availability-only-details tr.odd td.last {}

/* Email to a Friend */
.email-friend {}

/* Alerts */
.alert-price {}
.alert-stock {}

/********** < Product Prices */
.price { white-space:nowrap !important; }

.price-box {}
.price-box .price { font-weight:bold; }

/* Regular price */
.regular-price {}
.regular-price .price { font-weight:bold; }

/* Old price */
.old-price {}
.old-price .price-label { white-space:nowrap; }
.old-price .price { font-weight:bold; text-decoration:line-through; }

/* Special price */
.special-price {}
.special-price .price-label { font-weight:bold; white-space:nowrap; }
.special-price .price { font-weight:bold; }

/* Minimal price (as low as) */
.minimal-price {}
.minimal-price .price-label { font-weight:bold; white-space:nowrap; }

.minimal-price-link { display:block; }
.minimal-price-link .label {}
.minimal-price-link .price { font-weight:normal; }

/* Excluding tax */
.price-excluding-tax { display:block; }
.price-excluding-tax .label { white-space:nowrap; }
.price-excluding-tax .price { font-weight:normal; }

/* Including tax */
.price-including-tax { display:block; }
.price-including-tax .label { white-space:nowrap; }
.price-including-tax .price { font-weight:bold; }

/* Configured price */
.configured-price {}
.configured-price .price-label { font-weight:bold; white-space:nowrap; }
.configured-price .price { font-weight:bold; }

/* FPT */
.weee { display:block; font-size:11px; color:#444; }
.weee .price { font-size:11px; font-weight:normal; }

/* Excl tax (for order tables) */
.price-excl-tax  { display:block; }
.price-excl-tax .label { display:block; white-space:nowrap; }
.price-excl-tax .price { display:block; }

/* Incl tax (for order tables) */
.price-incl-tax { display:block; }
.price-incl-tax .label { display:block; white-space:nowrap; }
.price-incl-tax .price { display:block; font-weight:bold; }

/* Price range */
.price-from {}
.price-from .price-label { font-weight:bold; white-space:nowrap; }

.price-to {}
.price-to .price-label { font-weight:bold; white-space:nowrap; }

/* Price notice next to the options */
.price-notice { padding-left:10px; }
.price-notice .price { font-weight:bold; }

/* Price as configured */
.price-as-configured {}
.price-as-configured .price-label { font-weight:bold; white-space:nowrap; }

.price-box-bundle {}
/********** Product Prices > */

/* Tier Prices */
.tier-prices .price { font-weight:bold; }
.tier-prices .benefit {}

.tier-prices-grouped {}

/* Add to Links */
.add-to-links .separator { display:none; }

/* Add to Cart */
.add-to-cart label { float:left; margin-right:5px; }
.add-to-cart .qty { float:left; }
.add-to-cart button.button { float:left; margin-left:5px; }
.add-to-cart .paypal-logo { clear:left; text-align:right; }
.add-to-cart .paypal-logo .paypal-or { clear:both; display:block; margin:5px 60px 5px 0; }
.product-view .add-to-cart .paypal-logo { margin:0; }

/* Add to Links + Add to Cart */
.add-to-box {}
.add-to-box .add-to-cart { float:left; }
.add-to-box .or { float:left; margin:0 10px; }
.add-to-box .add-to-links { float:left; }


.product-view {}

.product-essential {}

.product-collateral .box-collateral { margin:0 0 15px; }

/* Product Images */
.product-view .product-img-box { float:left; width:267px; }
.col3-layout .product-view .product-img-box { float:none; margin:0 auto; }
.product-view .product-img-box .product-image { border:1px solid #ddd; }
.product-view .product-img-box .product-image-zoom { position:relative; width:265px; height:265px; overflow:hidden; z-index:9; }
.product-view .product-img-box .product-image-zoom img { position:absolute; left:0; top:0; cursor:move; }
.product-view .product-img-box .zoom-notice { text-align:center; }
.product-view .product-img-box .zoom { position:relative; z-index:9; height:18px; margin:0 auto 13px; padding:0 28px; background:url(../images/slider_bg.gif) 50% 50% no-repeat; cursor:pointer; }
.product-view .product-img-box .zoom.disabled { -moz-opacity:.3; -webkit-opacity:.3; -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";/*IE8*/ opacity:.3; }
.product-view .product-img-box .zoom #track { position:relative; height:18px; }
.product-view .product-img-box .zoom #handle { position:absolute; left:0; top:-1px; width:9px; height:22px; background:url(../images/magnifier_handle.gif) 0 0 no-repeat;  }
.product-view .product-img-box .zoom .btn-zoom-out { position:absolute; left:2px; top:0; }
.product-view .product-img-box .zoom .btn-zoom-in { position:absolute; right:2px; top:0; }
.product-view .product-img-box .more-views ul { margin-left:-4px }
.product-view .product-img-box .more-views li { float:left; padding:1px; background-color:#ddd; margin:0 0 8px 4px ; }

.product-image-popup { margin:0 auto; }
.product-image-popup .buttons-set { float:right; clear:none; border:0; margin:0; padding:0; }
.product-image-popup .nav { margin:0 100px; text-align:center; }
.product-image-popup .image { display:block; }
.product-image-popup .image-label {}

/* Product Shop */
.product-view .product-shop { float:right; width:445px; }
.col1-layout .product-view .product-shop { float:right; width:700px; }
.col3-layout .product-view .product-shop { float:none; width:auto; }
.product-view .product-name {}
.product-view .short-description {}

/* Product Options */
.product-options { padding:10px; margin:10px 0 0; border:1px solid #ddd; background-color:#f6f6f6; }
.product-options dt label { font-weight:bold; }
.product-options dt .qty-holder { float:right; }
.product-options dt .qty-holder label { vertical-align:middle; }
.product-options dt .qty-disabled { background:none; border:0; padding:3px; color:#000; }
.product-options dd { margin:10px 0; }
.product-options dl.last dd.last {}
.product-options dd input.input-text { width:98%; }
.product-options dd input.datetime-picker { width:150px; }
.product-options dd .time-picker { display:-moz-inline-box; display:inline-block; padding:2px 0; vertical-align:middle; }
.product-options dd textarea { width:98%; height:8em; }
.product-options dd select { width:100%; }
.product-options .options-list {}
.product-options .options-list input.radio { float:left; margin:3px -18px 0 0; }
.product-options .options-list input.checkbox { float:left; margin:3px -20px 0 0; }
.product-options .options-list .label { display:block; margin-left:20px; }
.product-options ul.validation-failed { padding:0 7px; }
.product-options p.required { padding:15px 0 0; }

.product-options-bottom { padding:10px; border:1px solid #ddd; border-top:0; }
.product-options-bottom .price-box { margin:10px 0; }

/* Grouped Product */
.product-view .grouped-items-table {}

/* Block: Description */
.product-view .box-description {}

/* Block: Additional */
.product-view .box-additional {}

/* Block: Upsell */
.product-view .box-up-sell {}
.product-view .box-up-sell .products-grid td { width:25%; }

/* Block: Tags */
.product-view .box-tags {}
.product-view .box-tags .form-add label { float:left; }
.product-view .box-tags .form-add .input-box { float:left; width:260px; margin:0 5px; }
.product-view .box-tags .form-add input.input-text { width:254px; }
.product-view .box-tags .form-add p { clear:both; }

/* Block: Reviews */
.product-view .box-reviews {}
.product-view .box-reviews .form-add {}

/* Send a Friend */
.send-friend {}
/* ======================================================================================= */


/* Content Styles ================================================================= */
.product-name { font-size:1em; font-weight:normal; }
.product-name a {}

/* Product Tags */
.tags-list li { display:inline; }

/* Advanced Search */
.advanced-search {}
.advanced-search-amount {}
.advanced-search-summary {}

/* CMS Home Page */
.cms-home .subtitle {}
.cms-index-index .subtitle {}

/* Sitemap */
.page-sitemap .links { text-align:right; margin:0 8px -22px 0; }
.page-sitemap .links a { text-decoration:none; position:relative; }
.page-sitemap .links a:hover { text-decoration:underline; }
.page-sitemap .sitemap { margin:12px; }
.page-sitemap .sitemap a {}
.page-sitemap .sitemap li { margin:3px 0; }
.page-sitemap .sitemap li.level-0 { margin:10px 0 0; font-weight:bold; }
.page-sitemap .sitemap li.level-0 a {}

/* RSS */
.rss-title h1 { background:url(../images/i_rss-big.png) 0 4px no-repeat; padding-left:27px; }
.rss-table .link-rss { display:block; line-height:1.35; background-position:0 2px; }
/* ======================================================================================= */


/* Shopping Cart ========================================================================= */
.cart {}

/* Checkout Types */
.cart .checkout-types { float:right; text-align:right; }
.cart .title-buttons .checkout-types li { float:left; margin:0 0 5px 5px; }
.cart .checkout-types .paypal-or { margin:0 8px; line-height:2.3; }
.cart .totals .checkout-types .paypal-or { clear:both; display:block; padding:3px 55px 8px 0; line-height:1.0; font-size:11px; }

/* Shopping Cart Table */
.cart-table .item-msg { font-size:10px; }

/* Shopping Cart Collateral boxes */
.cart .cart-collaterals { padding:25px 0 0; }
.cart .cart-collaterals .col2-set { float:left; width:700px; }

.cart .crosssell {}
.cart .crosssell h2 { font-size:13px; font-weight:bold; }
.cart .crosssell .product-image { float:left; width:75px; height:75px; border:1px solid #ddd; }
.cart .crosssell .product-details { margin-left:90px; }
.cart .crosssell li.item { margin:12px 0; }

/* Discount Codes & Estimate Shipping and Tax Boxes */
.cart .discount,
.cart .shipping {}

/* Shopping Cart Totals */
.cart .totals { float:right; }
.cart .totals table { width:100%; }
.cart .totals table th,
.cart .totals table td { padding:5px; }
.cart .totals table th { font-weight:bold; }
.cart .totals table tfoot th {}

/* Options Tool Tip */
.item-options dt { font-weight:bold; font-style:italic; }
.item-options dd { padding-left:10px; }
.truncated { cursor:help; }
.truncated a.dots { cursor:help; }
.truncated a.details { cursor:help; }
.truncated .truncated_full_value { position:relative; z-index:999; }
.truncated .truncated_full_value .item-options { position:absolute; top:-99999em; left:-99999em; z-index:999; width:250px; padding:8px; border:1px solid #ddd; background-color:#f6f6f6; }
.truncated .truncated_full_value .item-options > p { font-weight:bold; text-transform:uppercase; }
.truncated .show .item-options { top:-20px; left:50%; }
.col-left .truncated .show .item-options { left:30px; top:7px; }
.col-right .truncated .show .item-options  { left:-240px; top:7px; }
/* ======================================================================================= */


/* Checkout ============================================================================== */
/********** < Common Checkout Styles */
/* Shipping and Payment methods */
.sp-methods dt { font-weight:bold; }
.sp-methods .price { font-weight:bold; }
.sp-methods .form-list { padding-left:20px; }
.sp-methods select.month { width:150px; margin-right:10px; }
.sp-methods select.year { width:96px; }
.sp-methods input.cvv { width:3em !important; }

.sp-methods .checkmo-list li { margin:0 0 5px; }
.sp-methods .checkmo-list label { width:135px; padding-right:10px; text-align:right; }
.sp-methods .checkmo-list address { float:left; }

.sp-methods .centinel-logos a { margin-right:3px; }
.sp-methods .centinel-logos img { vertical-align:middle; }

.sp-methods .release-amounts { margin:0.5em 0 1em; }
.sp-methods .release-amounts button { float:left; margin:5px 10px 0 0; }

.please-wait { float:right; margin-right:5px; }
.please-wait img { vertical-align:middle; }
.cvv-what-is-this { cursor:help; margin-left:5px; }

/* Tooltip */
.tool-tip { border:1px solid #ddd; background-color:#f6f6f6; padding:5px; position:absolute; z-index:9999; }
.tool-tip .btn-close { text-align:right; }
.tool-tip .btn-close a { display:block; margin:0 0 0 auto; width:15px; height:15px; background:url(../images/btn_window_close.gif) 100% 0 no-repeat; text-align:left; text-indent:-999em; overflow:hidden; }
.tool-tip .tool-tip-content { padding:5px; }

/* Gift Messages */
.gift-messages {}
.gift-messages-form { border:1px solid #ddd; background-color:#f5f5f5; }
.gift-messages-form { position:relative; }
.gift-messages-form label { float:none !important; position:static !important; }
.gift-messages-form h4 {}
.gift-messages-form .whole-order {}
.gift-messages-form .item { margin:0 0 10px; }
.gift-messages-form .item .product-img-box { float:left; width:75px; }
.gift-messages-form .item .product-image { margin:0 0 7px; }
.gift-messages-form .item .number { margin:0; font-weight:bold; text-align:center; }
.gift-messages-form .item .details { margin-left:90px; }
.gift-messages-form .item .details .product-name {}

.gift-message-link { display:block; background:url(../images/bkg_collapse.gif) 0 4px no-repeat; padding-left:20px; }
.gift-message-link.expanded { background-position:0 -53px; }
.gift-message-row {}
.gift-message-row .btn-close { float:right; }
.gift-message dt strong { font-weight:bold; }

/* Checkout Agreements */
.checkout-agreements {}
.checkout-agreements li { margin:10px 0; }
.checkout-agreements .agreement-content { border:1px solid #ddd; background-color:#f6f6f6; padding:5px; height:10em; overflow:auto; }
.checkout-agreements .agree { padding:6px; }

/* Centinel */
.centinel {}
.centinel .authentication { border:1px solid #ddd; background:#fff; }
.centinel .authentication iframe { width:99%; height:400px; background:transparent !important; margin:0 !important; padding:0 !important; border:0 !important; }

/* Generic Info Set */
.info-set {}
/********** Common Checkout Styles > */

/* One Page Checkout */
.block-progress {}
.block-progress dt { font-weight:bold; }
.block-progress dt.complete,
.block-progress dd.complete { background-color:#f6f6f6; }

.opc { border-bottom:1px solid #ddd; position:relative; }
.opc li.section { border:1px solid #ddd; border-bottom:0; }

.opc .buttons-set.disabled button.button { display:none; }
.opc .buttons-set .please-wait { height:21px; line-height:21px; }

.opc .step-title { background-color:#f6f6f6; padding:0 5px; text-align:right; }
.opc .step-title .number { float:left; line-height:22px; margin-right:5px; }
.opc .step-title h2 { float:left; }
.opc .step-title a { display:none; float:right; }

.opc .allow .step-title { cursor:pointer; }
/*.opc .allow .step-title a { display:block; }*/

.opc .active {}
.opc .active .step-title { background-color:#ccc; cursor:default; }
/*.opc .active .step-title a { display:none; }*/

.opc .step { border-top:1px solid #ddd; padding:10px; position:relative; }
.opc .step .tool-tip { right:10px; }

.opc .order-review {}
.opc .order-review .authentication {}
.opc .order-review .warning-message {}

/* Multiple Addresses Checkout */
.checkout-progress { padding:0 50px; margin:10px 0; }
.checkout-progress li { float:left; width:20%; border-top:5px solid #ccc; text-align:center; color:#ccc; }
.checkout-progress li.active { border-top-color:#000; color:#000; }

.multiple-checkout { position:relative; }
.multiple-checkout .tool-tip { top:50%; margin-top:-120px; right:10px; }
.multiple-checkout .grand-total { font-size:1.5em; text-align:right; }
.multiple-checkout .grand-total big {}
.multiple-checkout .grand-total .price {}
/* ======================================================================================= */


/* Account Login/Create Pages ============================================================ */
.account-login {}
.account-login .new-users {}
.account-login .registered-users {}

.account-create {}
/* Account Login/Create Pages ============================================================ */

/* Captcha */
.captcha-note  {}
.captcha-image { float:left; position:relative; }
.captcha-img { border:1px solid #ccc; }
.registered-users .captcha-image    {}
#checkout-step-login .captcha-image {}
.captcha-reload { position:absolute; top:2px; right:2px;}
.captcha-reload.refreshing  { animation:rotate 1.5s infinite linear; -webkit-animation:rotate 1.5s infinite linear; -moz-animation:rotate 1.5s infinite linear; }

@-webkit-keyframes rotate {
    0% { -webkit-transform:rotate(0); }
    0% { -webkit-transform:rotate(-360deg); }
}
@-moz-keyframes rotate {
    0% { -moz-transform:rotate(0); }
    0% { -moz-transform:rotate(-360deg); }
}
@keyframes rotate {
    0% { transform:rotate(0); }
    0% { transform:rotate(-360deg); }
}

/* Remember Me Popup ===================================================================== */
.window-overlay { background:url(../images/window_overlay.png) repeat; background:rgba(0, 0, 0, 0.35); position:absolute; top:0; left:0; height:100%; width:100%; z-index:990; }

.remember-me label {}
.remember-me-popup {}
.remember-me-popup h3 {}
.remember-me-popup .remember-me-popup-head {}
.remember-me-popup .remember-me-popup-head .remember-me-popup-close {}
.remember-me-popup .remember-me-popup-body {}
.remember-me-popup .remember-me-popup-body a {}
/* Remember Me Popup ===================================================================== */


/* My Account ============================================================================= */
.my-account .title-buttons .link-rss { float:none; margin:0; }

/********** < Dashboard */
.dashboard {}
.dashboard .welcome-msg {}

/* Block: Recent Orders */
.dashboard .box-recent { margin:10px 0; }

/* Block: Account Information */
.dashboard .box-info {}

/* Block: Reviews */
.dashboard .box-reviews .number { float:left; font-size:10px; font-weight:bold; line-height:1; color:#fff; margin:3px -20px 0 0; padding:2px 3px; background:#ddd; }
.dashboard .box-reviews .details { margin-left:20px; }

/* Block: Tags */
.dashboard .box-tags .number { float:left; font-size:10px; font-weight:bold; line-height:1; color:#fff; margin:3px -20px 0 0; padding:2px 3px; background:#ddd; }
.dashboard .box-tags .details { margin-left:20px; }
/********** Dashboard > */

/* Address Book */
.addresses-list {}
.addresses-list-additional li.item {}

/* Order View */
.order-info { border:1px solid #ddd; padding:5px; }
.order-info dt,
.order-info dd,
.order-info ul,
.order-info li { display:inline; }
.order-info dt { font-weight:bold; }

.order-date { margin:10px 0; }

.order-info-box {}

.order-items { width:100%; overflow-x:auto; }

.order-additional { margin:15px 0; }
/* Order Gift Message */
.gift-message dt strong { color:#666; }
.gift-message dd { font-size:13px; margin:5px 0 0; }
/* Order Comments */
.order-about dt { font-weight:bold; }
.order-about dd { font-size:13px; margin:0 0 7px; }

.tracking-table { margin:0 0 15px; }
.tracking-table th { font-weight:bold; white-space:nowrap; }

.tracking-table-popup { width:100%; }
.tracking-table-popup th { font-weight:bold; white-space:nowrap; }
.tracking-table-popup th,
.tracking-table-popup td { padding:1px 8px; }

/* Order Print Pages */
.page-print .print-head {}
.page-print .print-head img { float:left; }
.page-print .print-head address { float:left; margin-left:15px; }
/* Price Rewrites */
.page-print .gift-message-link { display:none; }
.page-print .price-excl-tax,
.page-print .price-incl-tax { display:block; white-space:nowrap; }
.page-print .cart-price,
.page-print .price-excl-tax .label,
.page-print .price-incl-tax .label,
.page-print .price-excl-tax .price,
.page-print .price-incl-tax .price { display:inline; }

/* My Reviews */
.product-review .product-img-box { float:left; width:140px;  }
.product-review .product-img-box .product-image { display:block; width:125px; height:125px; }
.product-review .product-img-box .label { font-size:11px; margin:0 0 3px; }
.product-review .product-img-box .ratings .rating-box { float:none; display:block; margin:0 0 3px; }
.product-review .product-details { margin-left:150px; }
.product-review .product-name { font-size:16px; font-weight:bold; margin:0 0 10px; }
.product-review h3 {}
.product-review .ratings-table { margin:0 0 10px; }
.product-review dt { font-weight:bold; }
.product-review dd { font-size:13px; margin:5px 0 0; }
/* ======================================================================================= */


/* Footer ================================================================================ */
.footer { border:1px solid #ddd; padding:10px; }
.footer p { text-align:right; }
.footer address { text-align:right; }
.footer ul {}
.footer ul li { display:inline; }
.footer-container .bottom-container { text-align:center; }
/* ======================================================================================= */


/* Clears ================================================================================ */
.clearer:after,
.header-container:after,
.header-container .top-container:after,
.header:after,
.header .quick-access:after,
#nav:after,
.main:after,
.footer:after,
.footer-container .bottom-container:after,
.col-main:after,
.col2-set:after,
.col3-set:after,
.col4-set:after,
.search-autocomplete li:after,
.block .block-content:after,
.block .actions:after,
.block li.item:after,
.block-poll li:after,
.block-layered-nav .currently li:after,
.page-title:after,
.products-grid:after,
.products-list li.item:after,
.box-account .box-head:after,
.dashboard .box .box-title:after,
.box-reviews li.item:after,
.box-tags li.item:after,
.pager:after,
.sorter:after,
.ratings:after,
.add-to-box:after,
.add-to-cart:after,
.product-essential:after,
.product-collateral:after,
.product-view .product-img-box .more-views ul:after,
.product-view .product-shop .short-description:after,
.product-view .box-description:after,
.product-view .box-tags .form-add:after,
.product-options .options-list li:after,
.product-options-bottom:after,
.product-review:after,
.cart:after,
.cart-collaterals:after,
.cart .crosssell li.item:after,
.opc .step-title:after,
.checkout-progress:after,
.multiple-checkout .place-order:after,
.group-select li:after,
.form-list li:after,
.form-list .field:after,
.buttons-set:after,
.page-print .print-head:after,
.advanced-search-summary:after,
.gift-messages-form .item:after,
.send-friend .form-list li p:after { display:block; content:"."; clear:both; font-size:0; line-height:0; height:0; overflow:hidden; }
/* ======================================================================================= */
