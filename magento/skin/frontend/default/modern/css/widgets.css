/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_modern
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/* Widgets =============================================================================== */
.widget { display:inline; }
.widget .widget-title { clear:both; padding:15px 0 0; margin:0 0 7px; }
.widget .widget-title h2 { font-size:15px; font-weight:bold; color:#e25203; }

.widget .mini-products-images-list .product-image { display:block; width:76px; height:76px; border:1px solid #a9a9a9; margin:0 auto; }
.widget .mini-products-images-list li.item { float:left; width:77px; height:77px; border:0 !important; padding:5px 9px !important; }

/* Widget: Catalog New Proructs List */
.widget-new-products { display:block; }
.col-main .widget-new-products { margin:7px 0; }
.widget-new-products .products-grid .product-image,
.widget-new-products .products-list .product-image { width:85px; height:85px; }
.widget-new-products .products-list .product-shop { margin-left:100px; }

/* Widget: Recently Viewed Proructs */
.widget-viewed { display:block; }
.col-main .widget-viewed { margin:7px 0; }
.widget-viewed .products-grid .product-image,
.widget-viewed .products-list .product-image { width:85px; height:85px; }
.widget-viewed .products-list .product-shop { margin-left:100px; }

/* Widget: Recently Compared Proructs */
.widget-compared { display:block;}
.col-main .widget-compared { margin:7px 0; }
.widget-compared .products-grid .product-image,
.widget-compared .products-list .product-image { width:85px; height:85px; }
.widget-compared .products-list .product-shop { margin-left:100px; }

/* Widget: CMS Static Block */
.widget-static-block {}
.sidebar .widget-static-block { display:block; margin:0 0 15px; }

/* Widgets: Links Common Styles */
.top-container .widget a { padding:0 0 0 7px; }

.bottom-container .widget a { padding:0 3px; }

/* Widget: Catalog Product Link */
.widget-product-link-inline { display:inline; }

.col-main .widget-product-link { display:block; font-size:12px; text-align:right; margin:7px 0; }
.col-main .widget-product-link a { background:url(../images/widgets/i_widget-link.gif) 100% 3px no-repeat; padding-right:15px; }

.sidebar .widget-product-link { display:block; margin:0 0 15px; padding:0 10px; }
.sidebar .widget-product-link a { background:url(../images/widgets/i_widget-link.gif) 100% 3px no-repeat; padding:0 15px 0 0; }

/* Widget: Catalog Category Link */
.widget-category-link-inline { display:inline; }

.col-main .widget-category-link { display:block; font-size:12px; text-align:right; margin:7px 0; }
.col-main .widget-category-link a { background:url(../images/widgets/i_widget-link.gif) 100% 3px no-repeat; padding-right:15px; }

.sidebar .widget-category-link { display:block; margin:0 0 15px; padding:0 10px; }
.sidebar .widget-category-link a { background:url(../images/widgets/i_widget-link.gif) 100% 3px no-repeat; padding:0 15px 0 0; }

/* Widget: CMS Page Link */
.widget-cms-link-inline { display:inline; }

.col-main .widget-cms-link { display:block; font-size:12px; text-align:right; margin:7px 0; }
.col-main .widget-cms-link a { background:url(../images/widgets/i_widget-link.gif) 100% 3px no-repeat; padding-right:15px; }

.sidebar .widget-cms-link { display:block; margin:0 0 15px; padding:0 10px; }
.sidebar .widget-cms-link a { background:url(../images/widgets/i_widget-link.gif) 100% 3px no-repeat; padding:0 15px 0 0; }
/* ======================================================================================= */
