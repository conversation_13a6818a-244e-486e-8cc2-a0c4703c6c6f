//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

// =============================================
// Mixin - Image Replacement
// =============================================

// http://www.zeldman.com/2012/03/01/replacing-the-9999px-hack-new-image-replacement/
// http://nicolasgallagher.com/another-css-image-replacement-technique/
// https://github.com/h5bp/html5-boilerplate/issues/1005#issuecomment-4947702

@mixin image-replacement {
    // Note: this technique has issues on Android 3.x
    // An alternative that requires the :before generated content was added to H5BP
    // See: https://github.com/h5bp/html5-boilerplate/blob/master/css/main.css

    //color:transparent; font:0/0 a; text-shadow:none; vertical-align:middle;

    // The simplest option remains using text-indent:-9999px despite the performance drawback
    text-indent: -9999px;
}

// Undo the replacement for child elements

@mixin image-replacement-override {
    text-indent: 0;
}
