//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

// =============================================
// Mixin - Triangle
// =============================================

@mixin triangle($direction: up, $size: 5px, $color: #000000) {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    display: block;

    @if $direction == up {
        border-right: $size solid transparent;
        border-left: $size solid transparent;
        border-bottom: $size solid $color;
        border-top: none;
    }

    @if $direction == down {
        border-right: $size solid transparent;
        border-left: $size solid transparent;
        border-top: $size solid $color;
        border-bottom: none;
    }

    @if $direction == right {
        border-top: $size solid transparent;
        border-bottom: $size solid transparent;
        border-left: $size solid $color;
        border-right: none;
    }

    @if $direction == left {
        border-top: $size solid transparent;
        border-bottom: $size solid transparent;
        border-right: $size solid $color;
        border-left: none;
    }
}
