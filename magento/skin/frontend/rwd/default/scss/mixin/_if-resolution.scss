//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

// =============================================
// Mixin - If-Resolution
// =============================================

// From http://blog.14islands.com/post/37259603246/sass-resolution-mixin

@mixin if-min-resolution($dppx) {
    @include if-resolution(min, $dppx) {
        @content;
    }
}

@mixin if-max-resolution($dppx) {
    @include if-resolution(max, $dppx) {
        @content;
    }
}

@mixin if-resolution($prefix, $dppx) {
    // 1px = 96dpi
    $dpi: $dppx * 96;
    @media
    (-webkit-#{$prefix}-device-pixel-ratio: #{$dppx}),
    (   #{$prefix}--moz-device-pixel-ratio: #{$dppx}),
    (     -o-#{$prefix}-device-pixel-ratio: #{$dppx*2}/2),
    (        #{$prefix}-device-pixel-ratio: #{$dppx}),
    (                #{$prefix}-resolution: #{$dpi}dpi),
    (                #{$prefix}-resolution: #{$dppx}dppx) {
        @content;
    }
}

// ----------------------------------------------
// Usage example:
// ----------------------------------------------

//// Renders on screens with >= 2 resolution
//@include if-min-resolution (2) {
//    div { color:#000; }
//}
//
//// Renders on screens with >= 1.5 resolution
//@include if-min-resolution (1.5) {
//    div { color:#000; }
//}
//
//// Renders on screens with <= 1.5 resolution
//@include if-max-resolution (1.5) {
//    div { color:#000; }
//}
//
//// Renders on screens with <= 2 resolution
//@include if-max-resolution (2) {
//    div { color:#000; }
//}

// ----------------------------------------------

