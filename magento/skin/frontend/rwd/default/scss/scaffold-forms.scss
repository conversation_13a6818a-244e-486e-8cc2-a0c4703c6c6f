//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

@import "framework";

/* ============================================ *
 * SCAFFOLD FORM
 * ============================================ */

/* Scaffolding can not be applied globally to all forms, since some forms are best displayed in a stacked layout */

.scaffold-form {
    .form-list > li {
        display: block;

        &:after {
            @include clearfix;
        }
    }

    .fields {
        margin: 0;
        padding: 0;
        &:after {
            @include clearfix;
        }
    }

    .fields > .fields,
    .field,
    .wide,
    .control {
        margin-bottom: 7px;
        &:after {
            @include clearfix;
        }
    }

    label:first-child {
        float: left;
        width: 135px;
        padding: 7px 8px 0 0;
    }

    select,
    textarea,
    input[type=email],
    input[type=search],
    input[type=number],
    input[type=password],
    input[type=tel],
    input[type=text] {
        width: 330px;
        max-width: 100%;
    }

    .input-box {
        float: left;
        max-width: 350px;
    }

    .buttons-set {
        padding-top: 5px;
    }
}

@include bp(max-width, $bp-small) {

    .scaffold-form {

        label:first-child {
            float: none;
            width: auto;
            padding: 0;
        }

        .input-box {
            float: none;
            max-width: 100%;
        }
        .control .input-box {
            clear: none;
            float: left;
            display: inline;
            width: auto;
        }
        .control label:first-child {
            float: left;
        }
    }
}
