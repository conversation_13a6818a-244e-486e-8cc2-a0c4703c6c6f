//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

/* ============================================ *
 * Recurring Profiles
 * ============================================ */

#recurring_profile_list_view {

    th {
        white-space: normal;

        span {
            white-space: inherit;
        }
    }

    @include bp (max-width, $bp-xsmall) {
        font-size: $f-size-xs;

        a {
            font-size: inherit;
        }

        th,
        td {
            padding: 2px;
        }
    } //end bp-xsmall breakpoint

} //end #recurring_profile_list_view

.recurring-profiles-title {
    float: left;

    h1 {
        margin-bottom: $element-spacing;
    }
}

body.customer-account .my-account .title-buttons.recurring-profiles-title-buttons {
    float:right;
    text-align: right;
    margin-bottom: $trim;

    @include bp(max-width, $bp-xsmall) {
        text-align: center;
        float: none;
    }

    .button {
        position: static;
    }
}

.recurring-profiles-messages {
    &:after {
        @include clearfix;
    }
}

#recurring_start_date_trig {
    display: inline;
}
