//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

/* ============================================ *
 * Account - Reviews
 * ============================================ */

.product-review {
    .product-img-box {
        p.label {
            border-bottom: 1px solid $c-module-border;
            font-size: $f-size-xl;
            font-family: $f-stack-special;
            margin-top: 20px;
            padding-bottom: $gap;
            text-transform: uppercase;
        }

        .rating-box {
            margin: 15px 0;
        }
    }

    .product-details {
        h2 {
            border-bottom: 1px solid $c-module-border;
            color: #3399CC;
            font-size: $f-size-xl;
            font-weight: 600;
            font-family: $f-stack-special;
            padding-bottom: $gap;
        }
    }

    .ratings-table {
        font-family: $f-stack-special;

        th {
            padding-right: $gap;
        }

        tr {
            float: left;
            font-size: $f-size-xs;
            margin-right: $gap;
            text-transform: uppercase;
        }
    }

    .ratings-description {
        dt {
            border-bottom: 1px solid $c-module-border;
            font-size: $f-size-xl;
            font-weight: 400;
            font-family: $f-stack-special;
            margin-top: 20px;
            padding: $gap 0;
            text-transform: uppercase;
        }

        dd {
            color: #737373;
            font-family: $f-stack-special;
            font-size: $f-size-s;
        }
    }
}

// ---------------------------------------------

@include bp(min-width, $bp-medium + 1) {
    .product-review {
        .ratings-table {
            tr {
                float: none;
            }
        }
    }
}

// ---------------------------------------------

@include bp(min-width, 1126px) {
    .product-review {
        .ratings-table {
            tr {
                float: left;
                margin-right: 15px;
            }
        }
    }
}

// ---------------------------------------------

@include bp(max-width, 320px) {
    .product-review {
        .ratings-table {
            tr {
                float: none;
            }
        }
    }
}
