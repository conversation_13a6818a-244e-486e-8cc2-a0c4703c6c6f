//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

/* ============================================ *
 * Tags
 * ============================================ */

#addTagForm button {
    margin-top: 5px;
    margin-bottom: 10px;
}
#addTagForm .form-add {
    margin-top: 10px;
}
.product-tags {
    background-color: $c-module-background;
    border: 1px solid $c-module-border;
    float: left;
    margin-bottom: 10px;
    padding: 5px 1% 10px;
    width: 98%;
}
.product-tags li {
    float: left;
    margin-right: 15px;
}
.product-tags li.last {
    margin-right: 0px;
}
.tags-list {
    float: left;
    width: 100%;
    margin-bottom: 10px;
}
.tags-list > li {
    float: left;
    margin-right: 10px;
}
.block-tags .actions > a {
    font-size: 12px;
}
.my-tag-edit {
    .button.btn-remove {
        padding: 0;

        &:after {
            color: white;
        }
    }
}
@include bp (max-width, $bp-medium) {
    .tags-list > li,
    .tags-list {
        float: none;
    }
}
