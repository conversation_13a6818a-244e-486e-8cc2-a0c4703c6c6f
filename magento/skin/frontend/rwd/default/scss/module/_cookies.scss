//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

/* ============================================ *
 * Cookies
 * ============================================ */

.notice-cookie {
    .notice-inner {
        padding-bottom: 0;

        &:after {
            @include clearfix;
        }
    }

    .notice-text {
        float: left;
        max-width: 90%;
        padding-top: 4px;
        text-align: left;

        p {
            padding-bottom: (40px - $f-size-xxs) / 2;
            line-height: 1.3;
        }
    }

    .actions {
        float: left;
        text-align: right;
        padding-bottom: (40px - $f-size-xxs) / 2;
    }
}

// ---------------------------------------------

@include bp(max-width, $bp-large) {
    .notice-cookie {
        .notice-text {
            max-width: 86%;
        }
    }
}

// ---------------------------------------------

@include bp(max-width, $bp-medium) {

    .notice-cookie {
        padding: 0 20px;

        .notice-text {
            max-width: 82%;
            padding-top: 0;
        }
    }
}

// ---------------------------------------------

@include bp(max-width, 620px) {

    .notice-cookie {
        padding: 0 20px;

        .notice-inner {
            background-position: left 10px;

            &:after {
                @include clearfix;
            }
        }

        .notice-text {
            float: none;
            max-width: 100%;
            padding-top: 0;
        }

        .actions {
            text-align: left;
        }
    }
}
