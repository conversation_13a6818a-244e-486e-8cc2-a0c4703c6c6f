//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

/* ============================================ *
 * Widgets
 * ============================================ */

/* -------------------------------------------- *
 * Add to cart by SKU
 */

.sidebar {
    .widget-addbysku {
        .sku-table {
            .input-text:not(.qty) {
                width: 100%;

                @include bp(min-width, $bp-medium+1) {
                    //prevent widget in left/right column from exceeding column width
                    max-width: 100px;
                }
            }
        }
    }
}

.widget {
    clear: both;
}

.widget .pager {
    float: none;
    width: 100%;
}

// Implement custom grid breakpoints for grids that are contained within widgets
@include bp(min-width, $bp-xlarge) {
    .col2-left-layout,
    .col2-right-layout,
    .col3-layout
    {
        .col-main .widget {
            @include product-grid(4, 960, "widget");
            @include product-grid(5, 960, "widget");
        }
    }
}

@include bp(min-width, $bp-medium) {
    .col1-layout {
        .col-main .widget {
            @include product-grid(4, 960, "widget");
            @include product-grid(5, 960, "widget");
        }
    }
}
