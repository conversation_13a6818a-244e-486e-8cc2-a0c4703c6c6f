/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     rwd_default
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* ============================================ *
 * Homepage
 * ============================================ */
/* -------------------------------------------- *
 * Primary Banner
 */
body.cms-home .main-container {
  padding-top: 20px;
}
body.cms-home .slideshow-container {
  margin-top: 0;
}

.slideshow .banner-msg {
  position: absolute;
  text-align: center;
  text-transform: uppercase;
  width: 100%;
  top: 30%;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.slideshow .banner-msg h2 {
  color: #FFFFFF;
  font-size: 24px;
  text-shadow: 1px 1px 3px #555555;
}

.slideshow .banner-msg h2 strong {
  font-weight: bold;
  display: block;
  font-size: 36px;
}

/* -------------------------------------------- *
 * Promotion Banner Section
 */
.promos {
  margin: 0 0 10px 0;
  padding: 0;
  width: 100%;
}

.promos:after {
  content: '';
  display: table;
  clear: both;
}

/* Specifying the body only in order to override the .std ul li styling */
body .promos > li {
  margin: 0 0 10px 0;
  list-style: none;
  text-align: center;
  position: relative;
  border: 1px solid #cccccc;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.promos > li:last-child {
  margin-bottom: 0;
}

.promos img {
  max-width: 100%;
  width: 100%;
}

.promos a:hover {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
  opacity: 0.8;
  display: block;
}

.promos span {
  color: #FFFFFF;
  text-transform: uppercase;
  position: absolute;
  display: block;
  width: 100%;
  top: 10%;
  font-weight: 500;
  font-size: 20px;
  font-family: "Raleway", "Helvetica Neue", Verdana, Arial, sans-serif;
  text-shadow: 1px 1px 3px #555555;
}

.promos strong {
  font-weight: 600;
  font-size: 26px;
  display: block;
}

.promos span {
  font-size: 16px;
}

.promos strong {
  font-size: 17px;
}

/* Config: Three columns + flexible gutter */
body .promos > li {
  float: left;
  width: 31.74603%;
  margin-right: 2.38095%;
}

.promos > li:nth-child(3n) {
  margin-right: 0;
}

.promos span {
  font-size: 18px;
}

.promos strong {
  font-size: 24px;
}

/* -------------------------------------------- *
 * New Products Section
 */
/* Hiding the reviews content via CSS because we can't modify the template, */
/* since it may be used in other places where those elements are needed */
.cms-index-index .products-grid .ratings,
.cms-index-index .products-grid .actions,
.cms-index-noroute .products-grid .ratings,
.cms-index-noroute .products-grid .actions {
  display: none;
}

.cms-index-index h2.subtitle {
  padding: 6px 0;
  text-align: center;
  color: #3399cc;
  font-weight: 600;
  border-bottom: 1px solid #cccccc;
  border-top: 1px solid #cccccc;
}

.cms-index-noroute h2.subtitle {
  display: none;
}
