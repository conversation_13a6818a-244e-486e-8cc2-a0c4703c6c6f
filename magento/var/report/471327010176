a:4:{i:0;s:105:"SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo failed: Temporary failure in name resolution";i:1;s:2087:"#0 /magento/lib/Zend/Db/Adapter/Pdo/Mysql.php(111): Zend_Db_Adapter_Pdo_Abstract->_connect()
#1 /magento/lib/Varien/Db/Adapter/Pdo/Mysql.php(396): Zend_Db_Adapter_Pdo_Mysql->_connect()
#2 /magento/lib/Zend/Db/Adapter/Abstract.php(460): Varien_Db_Adapter_Pdo_Mysql->_connect()
#3 /magento/lib/Zend/Db/Adapter/Pdo/Abstract.php(238): Zend_Db_Adapter_Abstract->query('SET NAMES utf8', Array)
#4 /magento/lib/Varien/Db/Adapter/Pdo/Mysql.php(504): Zend_Db_Adapter_Pdo_Abstract->query('SET NAMES utf8', Array)
#5 /magento/app/code/core/Mage/Core/Model/Resource.php(179): Varien_Db_Adapter_Pdo_Mysql->query('SET NAMES utf8')
#6 /magento/app/code/core/Mage/Core/Model/Resource.php(110): Mage_Core_Model_Resource->_newConnection('pdo_mysql', Array)
#7 /magento/app/code/core/Mage/Core/Model/Resource/Db/Abstract.php(320): Mage_Core_Model_Resource->getConnection('core_write')
#8 /magento/app/code/core/Mage/Core/Model/Resource/Db/Abstract.php(350): Mage_Core_Model_Resource_Db_Abstract->_getConnection('write')
#9 /magento/app/code/core/Mage/Core/Model/Resource/Db/Abstract.php(335): Mage_Core_Model_Resource_Db_Abstract->_getWriteAdapter()
#10 /magento/app/code/core/Mage/Core/Model/Resource/Cache.php(53): Mage_Core_Model_Resource_Db_Abstract->_getReadAdapter()
#11 /magento/app/code/core/Mage/Core/Model/Cache.php(478): Mage_Core_Model_Resource_Cache->getAllOptions()
#12 /magento/app/code/core/Mage/Core/Model/Cache.php(520): Mage_Core_Model_Cache->_initOptions()
#13 /magento/app/code/core/Mage/Core/Model/App.php(1218): Mage_Core_Model_Cache->canUse('config')
#14 /magento/app/code/core/Mage/Core/Model/Config.php(417): Mage_Core_Model_App->useCache('config')
#15 /magento/app/code/core/Mage/Core/Model/Config.php(297): Mage_Core_Model_Config->_canUseCacheForInit()
#16 /magento/app/code/core/Mage/Core/Model/App.php(440): Mage_Core_Model_Config->loadModulesCache()
#17 /magento/app/code/core/Mage/Core/Model/App.php(370): Mage_Core_Model_App->_initModules()
#18 /magento/app/Mage.php(694): Mage_Core_Model_App->run(Array)
#19 /magento/index.php(86): Mage::run('default', 'store')
#20 {main}";s:3:"url";s:1:"/";s:11:"script_name";s:10:"/index.php";}